#!/usr/bin/env python3
"""
测试站点配置修复的脚本
"""

import json
import sys
import os

# 添加后端路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_site_configs_serialization():
    """测试站点配置的序列化和反序列化"""
    
    # 模拟前端发送的站点配置数据
    site_configs = [
        {
            "site_id": 1,
            "site_name": "测试站点1",
            "site_url": "https://test1.com",
            "keywords": ["关键词1", "关键词2"],
            "blog_category_id": 1,
            "blog_category_name": "技术",
            "blog_tags": [1, 2, 3]
        },
        {
            "site_id": 2,
            "site_name": "测试站点2", 
            "site_url": "https://test2.com",
            "keywords": ["关键词3", "关键词4"],
            "blog_category_id": 2,
            "blog_category_name": "生活",
            "blog_tags": [4, 5]
        }
    ]
    
    print("=== 测试站点配置序列化 ===")
    print("原始数据:")
    print(json.dumps(site_configs, indent=2, ensure_ascii=False))
    
    # 测试序列化（模拟后端处理）
    serialized = json.dumps(site_configs, ensure_ascii=False)
    print(f"\n序列化后的JSON字符串长度: {len(serialized)}")
    
    # 测试反序列化（模拟前端解析）
    try:
        deserialized = json.loads(serialized)
        print("\n反序列化成功!")
        print("反序列化后的数据:")
        print(json.dumps(deserialized, indent=2, ensure_ascii=False))
        
        # 验证数据完整性
        assert len(deserialized) == len(site_configs)
        assert deserialized[0]["site_id"] == 1
        assert deserialized[0]["site_name"] == "测试站点1"
        assert deserialized[0]["keywords"] == ["关键词1", "关键词2"]
        assert deserialized[0]["blog_tags"] == [1, 2, 3]
        
        print("\n✅ 数据完整性验证通过!")
        
    except Exception as e:
        print(f"\n❌ 反序列化失败: {e}")
        return False
    
    return True

def test_schema_validation():
    """测试Schema验证"""
    try:
        from app.schemas.scheduled_publish import SiteConfigItem, ScheduledPublishPlanUpdate
        
        print("\n=== 测试Schema验证 ===")
        
        # 测试SiteConfigItem
        site_config_data = {
            "site_id": 1,
            "site_name": "测试站点",
            "site_url": "https://test.com",
            "keywords": ["关键词1", "关键词2"],
            "blog_category_id": 1,
            "blog_category_name": "技术",
            "blog_tags": [1, 2, 3]
        }
        
        site_config = SiteConfigItem(**site_config_data)
        print("✅ SiteConfigItem 验证通过")
        print(f"站点配置: {site_config.site_name} - {len(site_config.keywords)} 个关键词")
        
        # 测试ScheduledPublishPlanUpdate
        update_data = {
            "plan_name": "测试计划",
            "site_configs": [site_config]
        }
        
        plan_update = ScheduledPublishPlanUpdate(**update_data)
        print("✅ ScheduledPublishPlanUpdate 验证通过")
        print(f"计划名称: {plan_update.plan_name}")
        print(f"站点配置数量: {len(plan_update.site_configs) if plan_update.site_configs else 0}")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试站点配置修复...")
    
    # 测试1: 序列化和反序列化
    test1_passed = test_site_configs_serialization()
    
    # 测试2: Schema验证
    test2_passed = test_schema_validation()
    
    print("\n=== 测试结果 ===")
    print(f"序列化测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"Schema测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过! 站点配置修复成功!")
        return 0
    else:
        print("\n💥 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    exit(main())
