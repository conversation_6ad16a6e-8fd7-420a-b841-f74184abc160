-- 添加执行时间字段到 scheduled_publish_plans 表
-- 执行日期: 2024-01-15
-- 描述: 为各种频率类型添加具体的执行时间配置

-- 1. 添加 execution_mode 字段（仅执行一次的执行模式）
ALTER TABLE scheduled_publish_plans
ADD COLUMN execution_mode VARCHAR(20) NULL
COMMENT '执行模式：immediate(立即执行) 或 scheduled(定时执行)';

-- 2. 添加 weekly_time 字段（每周执行时间）
ALTER TABLE scheduled_publish_plans
ADD COLUMN weekly_time TIME NULL
COMMENT '每周执行时间（HH:MM格式）';

-- 3. 添加 custom_time 字段（自定义间隔执行时间）
ALTER TABLE scheduled_publish_plans
ADD COLUMN custom_time TIME NULL
COMMENT '自定义间隔执行时间（HH:MM格式）';

-- 4. 为现有数据设置默认值
-- 对于 frequency_type = 'once' 的计划，设置为 'scheduled'（定时执行）
UPDATE scheduled_publish_plans
SET execution_mode = 'scheduled'
WHERE frequency_type = 'once' AND execution_mode IS NULL;

-- 对于 frequency_type = 'weekly' 的计划，设置默认执行时间为 09:00
UPDATE scheduled_publish_plans
SET weekly_time = '09:00:00'
WHERE frequency_type = 'weekly' AND weekly_time IS NULL;

-- 对于 frequency_type = 'custom' 且间隔单位不是 'hours' 的计划，设置默认执行时间为 09:00
UPDATE scheduled_publish_plans
SET custom_time = '09:00:00'
WHERE frequency_type = 'custom'
  AND custom_interval_unit != 'hours'
  AND custom_time IS NULL;

-- 5. 验证更新结果
SELECT
    id,
    plan_name,
    frequency_type,
    execution_mode,
    weekly_time,
    custom_time,
    daily_time,
    scheduled_time,
    created_at
FROM scheduled_publish_plans
ORDER BY created_at DESC
LIMIT 10;

-- 6. 检查所有新字段是否添加成功
DESCRIBE scheduled_publish_plans;

-- 或者使用以下查询检查新字段信息
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'scheduled_publish_plans'
    AND COLUMN_NAME IN ('execution_mode', 'weekly_time', 'custom_time')
ORDER BY COLUMN_NAME;
