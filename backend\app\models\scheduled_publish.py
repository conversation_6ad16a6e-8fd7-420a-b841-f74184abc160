from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, Time, BigInteger
from sqlalchemy.types import DECIMAL
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from .base import Base
from ..utils.datetime_utils import utc_now


class ScheduledTaskStatus(enum.Enum):
    """定时任务状态枚举"""
    PENDING = "pending"        # 待执行
    QUEUED = "queued"         # 排队中
    RUNNING = "running"       # 执行中
    SUCCESS = "success"       # 执行成功
    FAILED = "failed"         # 执行失败
    CANCELLED = "cancelled"   # 已取消


class FrequencyType(enum.Enum):
    """发布频率类型枚举"""
    ONCE = "once"           # 仅执行一次
    DAILY = "daily"         # 每天
    WEEKLY = "weekly"       # 每周
    CUSTOM = "custom"       # 自定义间隔


class ScheduledPublishPlan(Base):
    """定时发布计划模型"""
    __tablename__ = "scheduled_publish_plans"

    id = Column(Integer, primary_key=True, index=True)
    plan_name = Column(String(255), nullable=False, comment="任务计划名称")
    
    # 关键词配置
    keyword_source = Column(String(20), nullable=False, default="custom", comment="关键词来源：custom/library/strategy")
    keywords = Column(Text, nullable=True, comment="自定义关键词，逗号分隔")
    keyword_categories = Column(JSON, nullable=True, comment="选择的词库分类")
    keyword_selection_strategy = Column(String(50), nullable=True, default="random_one", comment="智能推荐策略：random_one等")

    # 选词策略配置
    use_keyword_strategy = Column(Boolean, nullable=False, default=False, comment="是否使用选词策略")
    strategy_intent = Column(String(100), nullable=True, comment="策略-关键词意图筛选")
    strategy_volume_min = Column(BigInteger, nullable=True, comment="策略-最小搜索量")
    strategy_volume_max = Column(BigInteger, nullable=True, comment="策略-最大搜索量")
    strategy_difficulty_min = Column(Integer, nullable=True, comment="策略-最小难度(0-100)")
    strategy_difficulty_max = Column(Integer, nullable=True, comment="策略-最大难度(0-100)")
    strategy_cpc_min = Column(DECIMAL(10, 2), nullable=True, comment="策略-最小CPC(USD)")
    strategy_cpc_max = Column(DECIMAL(10, 2), nullable=True, comment="策略-最大CPC(USD)")
    strategy_competitive_density_min = Column(DECIMAL(3, 2), nullable=True, comment="策略-最小竞争密度(0-1)")
    strategy_competitive_density_max = Column(DECIMAL(3, 2), nullable=True, comment="策略-最大竞争密度(0-1)")
    strategy_countries = Column(JSON, nullable=True, comment="策略-国家列表")
    strategy_categories = Column(JSON, nullable=True, comment="策略-分类列表")
    
    # 站点配置
    site_categories = Column(JSON, nullable=True, comment="选择的站点分类")
    selected_sites = Column(JSON, nullable=True, comment="自定义选择的站点ID列表")
    site_configs = Column(Text, nullable=True, comment="站点配置详情（JSON字符串）")
    
    # 发布配置
    default_blog_category = Column(String(100), nullable=True, comment="默认文章分类")
    default_blog_category_id = Column(Integer, nullable=True, comment="默认文章分类ID")
    default_blog_tags = Column(String(255), nullable=True, comment="默认博客标签ID，多个用逗号分隔")
    ai_model = Column(String(50), nullable=True, comment="AI模型")
    ai_config_id = Column(Integer, ForeignKey("ai_configs.id"), nullable=True, comment="AI配置ID")
    
    # 时间配置
    scheduled_time = Column(DateTime(timezone=True), nullable=False, comment="计划开始时间")
    end_time = Column(DateTime(timezone=True), nullable=True, comment="任务结束时间")
    
    # 频率配置
    frequency_type = Column(String(20), nullable=False, default="once", comment="发布频率类型：once/daily/weekly/custom")
    weekly_days = Column(JSON, nullable=True, comment="每周执行日期（0-6，0为周日）")
    weekly_time = Column(Time, nullable=True, comment="每周执行时间（HH:MM格式）")
    custom_interval_value = Column(Integer, nullable=True, comment="自定义间隔数值")
    custom_interval_unit = Column(String(10), nullable=True, comment="自定义间隔单位：hours/days/weeks")
    custom_time = Column(Time, nullable=True, comment="自定义间隔执行时间（HH:MM格式）")
    daily_time = Column(Time, nullable=True, comment="每日执行时间（HH:MM格式）")
    max_executions = Column(Integer, nullable=True, comment="最大执行次数限制")
    execution_mode = Column(String(20), nullable=True, comment="执行模式：immediate(立即执行) 或 scheduled(定时执行)")
    current_executions = Column(Integer, nullable=False, default=0, comment="当前已执行次数")
    next_execution_time = Column(DateTime(timezone=True), nullable=True, comment="下次执行时间")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime(timezone=True), default=utc_now, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now, comment="更新时间")
    
    # 执行统计
    last_execution_time = Column(DateTime(timezone=True), nullable=True, comment="上次执行时间")
    total_tasks = Column(Integer, default=0, comment="总任务数")
    success_tasks = Column(Integer, default=0, comment="成功任务数")
    
    # 关联关系
    ai_config = relationship("AIConfig")
    tasks = relationship("ScheduledPublishTask", back_populates="plan", cascade="all, delete-orphan")


class ScheduledPublishTask(Base):
    """定时发布任务模型"""
    __tablename__ = "scheduled_publish_tasks"

    id = Column(Integer, primary_key=True, index=True)
    plan_id = Column(Integer, ForeignKey("scheduled_publish_plans.id"), nullable=False, comment="计划ID")
    execution_sequence = Column(Integer, nullable=True, comment="执行序号（第几次执行）")
    scheduled_execution_time = Column(DateTime(timezone=True), nullable=True, comment="计划执行时间")
    
    # 任务配置
    keywords = Column(String(500), nullable=False, comment="分配的关键词")
    actual_keyword = Column(String(255), nullable=True, comment="实际选择的关键词（动态选词时使用）")
    wordpress_url = Column(String(255), nullable=False, comment="WordPress站点URL")
    site_name = Column(String(255), nullable=False, comment="站点名称")
    blog_category_id = Column(Integer, nullable=True, comment="文章分类ID")
    blog_category_name = Column(String(255), nullable=True, comment="文章分类名称")
    blog_tags = Column(String(255), nullable=True, comment="博客标签ID，多个用逗号分隔")
    ai_model = Column(String(50), nullable=True, comment="AI模型")
    
    # 执行状态
    status = Column(String(20), default="pending", comment="任务状态")
    queue_position = Column(Integer, nullable=True, comment="排队位置")
    execution_time = Column(DateTime(timezone=True), nullable=True, comment="实际执行时间")
    completion_time = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    
    # 执行结果
    ai_article_id = Column(Integer, ForeignKey("ai_articles.id"), nullable=True, comment="关联的AI文章ID")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    created_at = Column(DateTime(timezone=True), default=utc_now, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now, comment="更新时间")
    
    # 关联关系
    plan = relationship("ScheduledPublishPlan", back_populates="tasks")
    ai_article = relationship("AIArticle")


class TaskQueue(Base):
    """任务队列管理模型"""
    __tablename__ = "task_queue"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("scheduled_publish_tasks.id"), nullable=False, comment="任务ID")
    priority = Column(Integer, nullable=False, comment="优先级（基于计划创建时间）")
    status = Column(String(20), default="queued", comment="队列状态")
    worker_id = Column(String(100), nullable=True, comment="执行者ID")
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始执行时间")
    
    created_at = Column(DateTime(timezone=True), default=utc_now, comment="入队时间")
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now, comment="更新时间")
    
    # 关联关系
    task = relationship("ScheduledPublishTask") 