<template>
  <div class="keyword-library" :class="{ 'sidebar-collapsed': isCollapse }">
    <!-- 主要内容区域 - 左右布局 -->
    <div class="main-content">
      <!-- 左侧添加表单区域 - 隐藏 -->
      <div class="form-section hidden">
      </div>

      <!-- 中间分类筛选栏 -->
      <div class="category-filter-section">
        <el-card class="category-filter-card">
          <template #header>
            <div class="category-filter-header">
              <el-icon class="header-icon"><Collection /></el-icon>
              <span>分类导航</span>
            </div>
          </template>
          
          <!-- 分类搜索 -->
          <div class="category-search">
            <div class="search-with-sort">
              <el-input
                v-model="categorySearchKeyword"
                placeholder="搜索分类"
                clearable
                size="small"
                @input="handleCategorySearch"
                @clear="handleCategorySearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-dropdown @command="handleCategorySortChange" trigger="click">
                <el-button size="small" class="sort-button">
                  <el-icon><Sort /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="name-asc">按名称A-Z</el-dropdown-item>
                    <el-dropdown-item command="name-desc">按名称Z-A</el-dropdown-item>
                    <el-dropdown-item command="count-desc">按数量降序</el-dropdown-item>
                    <el-dropdown-item command="count-asc">按数量升序</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        
          <!-- 分类列表 -->
          <div class="category-list">
            <!-- 显示"全部"选项 -->
            <div
              class="category-item all-category"
              :class="{ 'active': searchForm.category === '' }"
              @click="handleCategorySelect('')"
            >
              <div class="category-content">
                <el-icon class="category-icon"><Grid /></el-icon>
                <span class="category-text">全部分类</span>
              </div>
              <span class="category-count">
                ({{ pagination.total }})
            </span>
            </div>

            <!-- 其他分类 -->
            <div
              v-for="category in paginatedCategories"
              :key="category"
              class="category-item"
              :class="{ 'active': searchForm.category === category }"
              @click="handleCategorySelect(category)"
              @contextmenu.prevent="showCategoryContextMenu($event, category)"
            >
              <div class="category-content">
                <el-icon class="category-icon"><Folder /></el-icon>
                <span class="category-text">{{ category }}</span>
              </div>
              <span class="category-count" v-if="categoryCounts[category]">
                ({{ categoryCounts[category] }})
              </span>
            </div>

            <!-- 显示"未分类"选项 - 放在最下方 -->
            <div
              class="category-item uncategorized-item"
              :class="{ 'active': searchForm.category === 'uncategorized' }"
              @click="handleCategorySelect('uncategorized')"
              @contextmenu.prevent="showUncategorizedContextMenu($event)"
            >
              <div class="category-content">
                <el-icon class="category-icon"><FolderOpened /></el-icon>
                <span class="category-text">未分类</span>
              </div>
              <span class="category-count" v-if="categoryCounts['uncategorized']">
                ({{ categoryCounts['uncategorized'] }})
              </span>
            </div>
          </div>
          
          <!-- 分类分页 -->
          <div class="category-pagination" v-if="filteredCategories.length > categoryPageSize">
            <el-pagination
              v-model:current-page="categoryPagination.page"
              :page-size="categoryPageSize"
              :total="filteredCategories.length"
              layout="prev, pager, next"
              small
              @current-change="handleCategoryPageChange"
            />
          </div>

          <!-- 分类管理按钮 -->
          <div class="category-actions">
            <el-button 
              type="primary" 
              size="small" 
              @click="showAddCategoryDialog"
              :icon="Plus"
            >
              添加
            </el-button>
            <el-button 
              size="small" 
              @click="showEditCategoryDialog"
              :icon="Edit"
              :disabled="!searchForm.category || searchForm.category === ''"
            >
              修改
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="confirmDeleteCategory"
              :icon="Delete"
              :disabled="!searchForm.category || searchForm.category === ''"
              class="delete-btn"
            >
              删除
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 右侧数据管理区域 -->
      <div class="data-section">
        <el-card class="data-card">
          <template #header>
            <div class="data-header">
              <div class="header-left">
                <el-icon class="header-icon"><Collection /></el-icon>
                <span>关键词列表</span>
                <el-tag type="info" size="small" style="margin-left: 10px;">
                  共 {{ pagination.total }} 条
                </el-tag>
              </div>
              <div class="header-right">
                <el-button-group>
                  <el-button @click="showImportDialog = true" size="small">
                    <el-icon><Upload /></el-icon>
                    导入
                  </el-button>
                  <el-button @click="handleExport" size="small">
                    <el-icon><Download /></el-icon>
                    导出
                  </el-button>
                  <el-button @click="handleGoogleAdsImport" size="small">
                    <el-icon><DataLine /></el-icon>
                    Google Ads
                  </el-button>
                  <el-button @click="handlePyTrendsImport" size="small">
                    <el-icon><TrendCharts /></el-icon>
                    PyTrends
                  </el-button>
                  <el-button @click="showStatsDialog = true" size="small">
                    <el-icon><Grid /></el-icon>
                    统计
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </template>
          
          <!-- 搜索筛选区域 -->
          <div class="search-section">
            <!-- 主要筛选条件 -->
            <el-row :gutter="16" class="main-filters">
              <el-col :span="5">
                <el-input
                  v-model="searchForm.keyword"
                  placeholder="搜索关键词"
                  clearable
                  @keyup.enter="handleSearch"
                  @input="handleAutoSearch"
                  @clear="handleAutoSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-select
                  v-model="searchForm.intent"
                  placeholder="意图"
                  clearable
                  @change="handleAutoSearch"
                  @clear="handleAutoSearch"
                >
                  <el-option label="全部" value="" />
                  <el-option label="信息型" value="Informational" />
                  <el-option label="商业型" value="Commercial" />
                  <el-option label="导航型" value="Navigational" />
                  <el-option label="交易型" value="Transactional" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select
                  v-model="searchForm.category"
                  placeholder="分类"
                  clearable
                  filterable
                  @change="handleAutoSearch"
                  @clear="handleAutoSearch"
                >
                  <el-option label="全部" value="" />
                  <el-option
                    v-for="category in categoryList"
                    :key="category"
                    :label="category"
                    :value="category"
                  />
                </el-select>
              </el-col>
              <el-col :span="7">
                <div class="slider-container">
                  <label class="slider-label">难度: {{ searchForm.min_difficulty || 1 }} - {{ searchForm.max_difficulty || 100 }}</label>
                  <el-slider
                    v-model="difficultyRange"
                    range
                    :min="1"
                    :max="100"
                    :step="1"
                    @change="handleDifficultyChange"
                    show-stops
                    :marks="{ 1: '1', 25: '25', 50: '50', 75: '75', 100: '100' }"
                  />
                </div>
              </el-col>
              <el-col :span="4">
                <div class="advanced-toggle-container">
                  <el-button
                    type="info"
                    size="default"
                    @click="isAdvancedSearchExpanded = !isAdvancedSearchExpanded"
                    class="advanced-toggle-btn"
                  >
                    <el-icon><Setting /></el-icon>
                    <span class="btn-text">高级筛选</span>
                    <el-icon class="expand-icon" :class="{ 'expanded': isAdvancedSearchExpanded }">
                      <ArrowDown />
                    </el-icon>
                  </el-button>
                </div>
              </el-col>
            </el-row>

            <!-- 高级筛选条件 -->
            <el-collapse-transition>
              <div v-show="isAdvancedSearchExpanded" class="advanced-filters">
                <div class="advanced-filters-title">
                  <el-icon><Setting /></el-icon>
                  <span>高级筛选选项</span>
                </div>

                <!-- 第一行 -->
                <el-row :gutter="20" class="advanced-row">
                  <el-col :span="6">
                    <div class="filter-item">
                      <label class="filter-label">标签</label>
                      <el-input
                        v-model="searchForm.tags"
                        placeholder="输入标签关键词"
                        clearable
                        @input="handleAutoSearch"
                        @clear="handleAutoSearch"
                      />
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="filter-item">
                      <label class="filter-label">CPC范围 (USD)</label>
                      <div class="range-input-group">
                        <el-input-number
                          v-model="searchForm.min_cpc"
                          placeholder="最低"
                          :min="0"
                          :precision="2"
                          size="default"
                          @change="handleAutoSearch"
                          style="width: 48%"
                        />
                        <span class="range-separator">-</span>
                        <el-input-number
                          v-model="searchForm.max_cpc"
                          placeholder="最高"
                          :min="0"
                          :precision="2"
                          size="default"
                          @change="handleAutoSearch"
                          style="width: 48%"
                        />
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="filter-item">
                      <label class="filter-label">竞争密度: {{ (searchForm.min_competitive_density || 0).toFixed(2) }} - {{ (searchForm.max_competitive_density || 1).toFixed(2) }}</label>
                      <el-slider
                        v-model="competitiveDensityRange"
                        range
                        :min="0"
                        :max="1"
                        :step="0.01"
                        @change="handleCompetitiveDensityChange"
                        :marks="{ 0: '0.00', 0.25: '0.25', 0.5: '0.50', 0.75: '0.75', 1: '1.00' }"
                      />
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="filter-item">
                      <label class="filter-label">搜索量范围</label>
                      <div class="range-input-group">
                        <el-input-number
                          v-model="advancedSearchForm.min_volume"
                          placeholder="最低"
                          :min="0"
                          size="default"
                          @change="handleAutoSearch"
                          style="width: 48%"
                        />
                        <span class="range-separator">-</span>
                        <el-input-number
                          v-model="advancedSearchForm.max_volume"
                          placeholder="最高"
                          :min="0"
                          size="default"
                          @change="handleAutoSearch"
                          style="width: 48%"
                        />
                      </div>
                    </div>
                  </el-col>
                </el-row>

                <!-- 第二行 -->
                <el-row :gutter="20" class="advanced-row">
                  <el-col :span="6">
                    <div class="filter-item">
                      <label class="filter-label">趋势</label>
                      <el-select
                        v-model="advancedSearchForm.trend"
                        placeholder="选择趋势类型"
                        clearable
                        @change="handleAutoSearch"
                        @clear="handleAutoSearch"
                      >
                        <el-option label="全部" value="" />
                        <el-option label="上升" value="up" />
                        <el-option label="下降" value="down" />
                        <el-option label="稳定" value="stable" />
                      </el-select>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="filter-item">
                      <label class="filter-label">搜索结果数</label>
                      <div class="range-input-group">
                        <el-input-number
                          v-model="advancedSearchForm.min_results"
                          placeholder="最低"
                          :min="0"
                          size="default"
                          @change="handleAutoSearch"
                          style="width: 48%"
                        />
                        <span class="range-separator">-</span>
                        <el-input-number
                          v-model="advancedSearchForm.max_results"
                          placeholder="最高"
                          :min="0"
                          size="default"
                          @change="handleAutoSearch"
                          style="width: 48%"
                        />
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="filter-item">
                      <label class="filter-label">国家/地区</label>
                      <el-select
                        v-model="advancedSearchForm.location_ids"
                        placeholder="选择国家或地区"
                        clearable
                        @change="handleAutoSearch"
                        @clear="handleAutoSearch"
                      >
                        <el-option label="全部" value="" />
                        <el-option label="全球" value="global" />
                        <el-option label="美国" value="us" />
                        <el-option label="英国" value="uk" />
                        <el-option label="德国" value="de" />
                        <el-option label="法国" value="fr" />
                        <el-option label="中国" value="cn" />
                      </el-select>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="filter-item">
                      <label class="filter-label">操作</label>
                      <el-button
                        type="primary"
                        @click="resetAdvancedSearch"
                        style="width: 100%"
                      >
                        <el-icon><Refresh /></el-icon>
                        重置高级筛选
                      </el-button>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-collapse-transition>


          </div>

          <!-- 数据表格 -->
          <div class="table-section">
            <!-- 表头控制区域 -->
            <div class="table-header-controls">
              <div class="table-header-left">
                <el-checkbox
                  v-model="selectAllChecked"
                  :indeterminate="selectAllIndeterminate"
                  @change="handleSelectAllChange"
                  class="select-all-checkbox"
                >
                  选中所有 ({{ selectAllChecked ? allFilteredKeywordIds.length : selectedRows.length }})
                </el-checkbox>
                <span class="select-info" v-if="selectAllChecked">
                  已选中当前筛选条件下的所有 {{ allFilteredKeywordIds.length }} 个关键词
                </span>
              </div>

              <div class="table-header-right">
                <div class="action-buttons-group">
                  <el-button type="primary" size="small" @click="resetSearch" class="action-btn">
                    <el-icon><Refresh /></el-icon>
                    重置
                  </el-button>
                  <el-button
                    type="warning"
                    size="small"
                    @click="handleBatchUpdate"
                    class="action-btn"
                    :disabled="!isSelectingAll && selectedRows.length === 0"
                  >
                    <el-icon><Edit /></el-icon>
                    更改
                  </el-button>
                  <el-button type="success" size="small" @click="handleAddKeyword" class="action-btn">
                    <el-icon><Plus /></el-icon>
                    添加
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="handleBatchDelete"
                    class="action-btn"
                    :disabled="!isSelectingAll && selectedRows.length === 0"
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </div>
            </div>

            <el-table
              ref="tableRef"
              :data="tableData"
              v-loading="loading"
              @selection-change="handleSelectionChange"
              @sort-change="handleSortChange"
              height="calc(100vh - 420px)"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="keyword_name" label="关键词" min-width="150" sortable />

              <el-table-column prop="intent" width="100">
                <template #header>
                  <el-tooltip
                    content="关键词意图类型：信息型（寻找信息）、商业型（比较产品）、导航型（寻找特定网站）、交易型（准备购买）"
                    placement="top"
                    effect="dark"
                  >
                    <span class="table-header-with-help">意图</span>
                  </el-tooltip>
                </template>
                <template #default="{ row }">
                  <el-tag v-if="row.intent" size="small" :type="getIntentTagType(row.intent)">
                    {{ getIntentText(row.intent) }}
                  </el-tag>
                  <span v-else class="text-muted">--</span>
                </template>
              </el-table-column>

              <el-table-column prop="volume" width="100" sortable>
                <template #header>
                  <el-tooltip
                    content="关键词在过去12个月的平均月搜索量"
                    placement="top"
                    effect="dark"
                  >
                    <span class="table-header-with-help">搜索量</span>
                  </el-tooltip>
                </template>
                <template #default="{ row }">
                  {{ formatNumber(row.volume || row.avg_monthly_searches) }}
                </template>
              </el-table-column>

              <el-table-column prop="trend" width="150">
                <template #header>
                  <el-tooltip
                    content="12个月内搜索者对特定关键词的兴趣。该指标根据每月查询量的变化而定"
                    placement="top"
                    effect="dark"
                  >
                    <span class="table-header-with-help">趋势</span>
                  </el-tooltip>
                </template>
                <template #default="{ row }">
                  <div v-if="row.trend" class="trend-display">
                    <div class="trend-chart-simple" :class="getTrendDirection(row.trend)">
                      <div
                        v-for="(point, index) in getTrendPoints(row.trend)"
                        :key="index"
                        class="trend-point"
                        :style="{ height: point + '%' }"
                      ></div>
                    </div>
                    <span class="trend-text">{{ getTrendSummary(row.trend) }}</span>
                  </div>
                  <span v-else class="text-muted">--</span>
                </template>
              </el-table-column>

              <el-table-column prop="keyword_difficulty" width="80" sortable>
                <template #header>
                  <el-tooltip
                    content="关键词难度（KD%）是衡量内容在Google前10名有机排名所需SEO努力的指标。分数从0到100，百分比越高，Semrush预测排名越困难。0-14=非常容易，15-29=容易，30-49=可能，50-69=困难，70-84=很难，85-100=非常困难。"
                    placement="top"
                    effect="dark"
                    :show-after="200"
                    popper-class="wide-tooltip"
                  >
                    <span class="table-header-with-help">难度</span>
                  </el-tooltip>
                </template>
                <template #default="{ row }">
                  <span v-if="row.keyword_difficulty !== null">{{ row.keyword_difficulty }}</span>
                  <span v-else class="text-muted">--</span>
                </template>
              </el-table-column>

              <el-table-column prop="cpc_usd" width="100" sortable>
                <template #header>
                  <el-tooltip
                    content="每次点击费用：广告主为用户点击由所列关键词触发的广告而支付的平均价格（美元）"
                    placement="top"
                    effect="dark"
                  >
                    <span class="table-header-with-help">CPC (USD)</span>
                  </el-tooltip>
                </template>
                <template #default="{ row }">
                  <span v-if="row.cpc_usd !== null">${{ row.cpc_usd.toFixed(2) }}</span>
                  <span v-else class="text-muted">--</span>
                </template>
              </el-table-column>

              <el-table-column prop="competitive_density" width="100" sortable>
                <template #header>
                  <el-tooltip
                    content="广告主在其PPC广告系列中针对所列关键词出价的竞争程度。竞争密度以0到1.00的等级显示，1.00表示排名最困难。"
                    placement="top"
                    effect="dark"
                  >
                    <span class="table-header-with-help">竞争密度</span>
                  </el-tooltip>
                </template>
                <template #default="{ row }">
                  <span v-if="row.competitive_density !== null">{{ (row.competitive_density).toFixed(2) }}</span>
                  <span v-else class="text-muted">--</span>
                </template>
              </el-table-column>

              <el-table-column prop="number_of_results" width="120" sortable>
                <template #header>
                  <el-tooltip
                    content="特定关键词在自然搜索结果中显示的URL数量"
                    placement="top"
                    effect="dark"
                  >
                    <span class="table-header-with-help">搜索结果数</span>
                  </el-tooltip>
                </template>
                <template #default="{ row }">
                  <span v-if="row.number_of_results !== null">{{ formatNumber(row.number_of_results) }}</span>
                  <span v-else class="text-muted">--</span>
                </template>
              </el-table-column>

              <el-table-column prop="location_ids" label="国家" width="80">
                <template #default="{ row }">
                  <span v-if="row.location_ids">{{ getCountryName(row.location_ids) }}</span>
                  <span v-else class="text-muted">全球</span>
                </template>
              </el-table-column>
              <el-table-column prop="category" label="分类" width="100">
                <template #default="{ row }">
                  <el-tag v-if="row.category" type="primary" size="small">
                    {{ row.category }}
              </el-tag>
                  <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
              <el-table-column prop="tags" label="标签" width="120" />
              <el-table-column prop="created_at" label="创建时间" width="160" sortable>
                <template #default="{ row }">
                  {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" link size="small" @click="handleEdit(row)">
              编辑
            </el-button>
                  <el-button type="danger" link size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
          <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.page"
              v-model:page-size="pagination.limit"
              :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
          </div>
        </el-card>
      </div>
    </div>

    <!-- 对话框组件 -->
    <KeywordDialog
      :visible="showCreateDialog"
      :keyword-data="currentKeyword"
      :is-edit="isEdit"
      @update:visible="showCreateDialog = $event"
      @success="handleDialogConfirm"
    />

    <ImportDialog
      :visible="showImportDialog"
      @update:visible="showImportDialog = $event"
      @success="handleImportConfirm"
      @refresh-categories="loadCategories"
    />

    <GoogleAdsImportDialog
      :visible="showGoogleAdsImportDialog"
      @update:visible="showGoogleAdsImportDialog = $event"
      @success="handleGoogleAdsImportConfirm"
    />

    <PyTrendsImportDialog
      :visible="showPyTrendsImportDialog"
      @update:visible="showPyTrendsImportDialog = $event"
      @success="handlePyTrendsImportConfirm"
    />

    <StatsDialog
      :visible="showStatsDialog"
      :stats="stats"
      @update:visible="showStatsDialog = $event"
    />

    <KeywordDetailDialog
      :visible="showDetailDialog"
      :keyword="currentKeyword"
      @update:visible="showDetailDialog = $event"
    />

    <!-- 右键菜单 -->
    <div
      v-if="contextMenu.visible"
      class="context-menu"
      :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
      @click.stop
    >
      <!-- 普通分类的菜单 -->
      <template v-if="!contextMenu.isUncategorized">
        <div class="context-menu-item" @click="showEditCategoryDialog">
          <el-icon><Edit /></el-icon>
          修改
        </div>
        <div class="context-menu-item" @click="showAddCategoryDialog">
          <el-icon><Plus /></el-icon>
          添加
        </div>
        <div class="context-menu-item danger" @click="confirmDeleteCategory">
          <el-icon><Delete /></el-icon>
          删除
        </div>
      </template>

      <!-- 未分类的菜单 -->
      <template v-else>
        <div class="context-menu-item danger" @click="confirmClearUncategorized">
          <el-icon><Delete /></el-icon>
          清空未分类
        </div>
      </template>
    </div>

    <!-- 添加分类对话框 -->
    <el-dialog
      v-model="addCategoryDialogVisible"
      title="添加分类"
      width="400px"
      @close="resetCategoryForm"
    >
      <el-form
        ref="addCategoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="80px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input
            v-model="categoryForm.name"
            placeholder="请输入分类名称"
            clearable
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addCategoryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddCategory" :loading="categorySubmitting">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改分类对话框 -->
    <el-dialog
      v-model="editCategoryDialogVisible"
      title="修改分类"
      width="400px"
      @close="resetCategoryForm"
    >
      <el-form
        ref="editCategoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="80px"
      >
        <el-form-item label="原分类">
          <el-input v-model="categoryForm.oldName" disabled />
        </el-form-item>
        <el-form-item label="新分类" prop="name">
          <el-input
            v-model="categoryForm.name"
            placeholder="请输入新的分类名称"
            clearable
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editCategoryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEditCategory" :loading="categorySubmitting">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量更改分类对话框 -->
    <el-dialog
      v-model="batchUpdateDialogVisible"
      title="批量更改分类"
      width="500px"
      @close="resetBatchUpdateForm"
    >
      <div class="batch-update-content">
        <div class="selected-info">
          <el-text v-if="isSelectingAll">
            已选择当前筛选条件下的所有 <strong>{{ allFilteredKeywordIds.length }}</strong> 个关键词
          </el-text>
          <el-text v-else>
            已选择 <strong>{{ selectedRows.length }}</strong> 个关键词
          </el-text>
        </div>
        
        <el-form
          ref="batchUpdateFormRef"
          :model="batchUpdateForm"
          :rules="batchUpdateRules"
          label-width="100px"
        >
          <el-form-item label="新分类" prop="category">
            <el-select 
              v-model="batchUpdateForm.category" 
              placeholder="请选择或输入新分类"
              filterable
              allow-create
              default-first-option
              :reserve-keyword="false"
              style="width: 100%;"
            >
              <el-option 
                v-for="category in categoryList" 
                :key="category" 
                :label="category" 
                :value="category" 
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchUpdateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleBatchUpdateConfirm" :loading="batchUpdateSubmitting">
            确定更改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Upload,
  Download,
  DataLine,
  Refresh,
  Search,
  Collection,
  Grid,
  Fold,
  TrendCharts,
  Edit,
  Delete,
  Sort,
  Folder,
  FolderOpened,
  Setting,
  ArrowDown
} from '@element-plus/icons-vue'
import KeywordDialog from '@/components/keyword/KeywordDialog.vue'
import ImportDialog from '@/components/keyword/ImportDialog.vue'
import GoogleAdsImportDialog from '@/components/keyword/GoogleAdsImportDialog.vue'
import PyTrendsImportDialog from '@/components/keyword/PyTrendsImportDialog.vue'
import StatsDialog from '@/components/keyword/StatsDialog.vue'
import KeywordDetailDialog from '@/components/keyword/KeywordDetailDialog.vue'
import { keywordService } from '@/services/keyword'
import { formatDateTime as formatDateTimeWithTimezone } from '@/utils/timezone.js'

export default {
  name: 'KeywordLibrary',
  components: {
    KeywordDialog,
    ImportDialog,
    GoogleAdsImportDialog,
    PyTrendsImportDialog,
    StatsDialog,
    KeywordDetailDialog
  },
  setup() {
    const store = useStore()
    
    // 获取侧边栏收起状态
    const isCollapse = computed(() => store.state.isCollapse)
    
    // 响应式数据
    const loading = ref(false)
    const tableData = ref([])
    const selectedItems = ref([])
    const stats = ref({})
    const categoryList = ref([]) // 分类列表
    const showCreateDialog = ref(false)
    const showImportDialog = ref(false)
    const showGoogleAdsImportDialog = ref(false)
    const showPyTrendsImportDialog = ref(false)
    const showStatsDialog = ref(false)
    const showDetailDialog = ref(false)
    const currentKeyword = ref(null)
    const isEdit = ref(false)
    const formRef = ref(null)
    const isFormCollapsed = ref(true) // 默认折叠状态
    
    // 分类筛选相关数据
    const categorySearchKeyword = ref('')
    const categoryPageSize = 10
    const categoryPagination = reactive({
      page: 1
    })
    const categoryCounts = ref({}) // 每个分类的关键词数量
    const categorySortOrder = ref('name-asc') // 默认按名称升序

    // 分类管理相关
    const addCategoryDialogVisible = ref(false)
    const editCategoryDialogVisible = ref(false)
    const categorySubmitting = ref(false)
    const addCategoryFormRef = ref()
    const editCategoryFormRef = ref()
    const contextMenu = reactive({
      visible: false,
      x: 0,
      y: 0,
      category: '',
      isUncategorized: false
    })

    const categoryForm = reactive({
      name: '',
      oldName: ''
    })

    const categoryRules = {
      name: [
        { required: true, message: '请输入分类名称', trigger: 'blur' },
        { min: 1, max: 50, message: '分类名称长度在 1 到 50 个字符', trigger: 'blur' }
      ]
    }

    // 表单数据
    const form = reactive({
      keyword: '',
      searchVolume: null,
      competition: 'low',
      cpc: null,
      country: 'UK',
      category: '',
      tags: '',
      notes: ''
    })
    
    // 表单验证规则
    const rules = reactive({
      keyword: [
        { required: true, message: '请输入关键词', trigger: 'blur' },
        { min: 1, max: 100, message: '关键词长度在 1 到 100 个字符', trigger: 'blur' }
      ],
      searchVolume: [
        { type: 'number', message: '搜索量必须是数字', trigger: 'blur' }
      ],
      competition: [
        { required: true, message: '请选择竞争度', trigger: 'change' }
      ],
      cpc: [
        { type: 'number', message: 'CPC必须是数字', trigger: 'blur' }
      ],
      country: [
        { required: true, message: '请选择国家', trigger: 'change' }
      ],
      category: [
        { required: true, message: '请选择或输入分类', trigger: 'blur' }
      ]
    })

    // 搜索表单
    const searchForm = reactive({
      keyword: '',
      intent: '',
      category: '',
      tags: '',
      // CPC范围
      min_cpc: null,
      max_cpc: null,
      // 难度范围
      min_difficulty: null,
      max_difficulty: null,
      // 竞争密度范围
      min_competitive_density: null,
      max_competitive_density: null,
      sort_by: 'updated_at',
      sort_order: 'desc'
    })

    // 高级筛选表单
    const advancedSearchForm = reactive({
      // 搜索量范围
      min_volume: null,
      max_volume: null,
      // 趋势筛选
      trend: '',
      // 搜索结果数范围
      min_results: null,
      max_results: null,
      // 国家筛选
      location_ids: ''
    })

    // 高级筛选展开状态
    const isAdvancedSearchExpanded = ref(false)

    // 滑块相关数据
    const difficultyRange = ref([1, 100])
    const competitiveDensityRange = ref([0, 1])

    // 分页
    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0,
      total_pages: 0
    })

    // 分类筛选相关计算属性
    const filteredCategories = computed(() => {
      let result = categoryList.value
      
      // 先过滤
      if (categorySearchKeyword.value) {
        result = result.filter(category => 
          category.toLowerCase().includes(categorySearchKeyword.value.toLowerCase())
        )
      }
      
      // 再排序
      const sorted = [...result]
      switch (categorySortOrder.value) {
        case 'name-asc':
          sorted.sort((a, b) => a.localeCompare(b, 'zh-CN'))
          break
        case 'name-desc':
          sorted.sort((a, b) => b.localeCompare(a, 'zh-CN'))
          break
        case 'count-desc':
          sorted.sort((a, b) => (categoryCounts.value[b] || 0) - (categoryCounts.value[a] || 0))
          break
        case 'count-asc':
          sorted.sort((a, b) => (categoryCounts.value[a] || 0) - (categoryCounts.value[b] || 0))
          break
      }
      
      return sorted
    })

    const paginatedCategories = computed(() => {
      const start = (categoryPagination.page - 1) * categoryPageSize
      const end = start + categoryPageSize
      return filteredCategories.value.slice(start, end)
    })

    // 格式化日期时间 - 使用全局时区配置
    const formatDateTime = (dateTime) => {
      return formatDateTimeWithTimezone(dateTime)
    }

    // 获取意图标签类型
    const getIntentTagType = (intent) => {
      const typeMap = {
        Informational: 'info',
        Commercial: 'warning',
        Navigational: 'primary',
        Transactional: 'success'
      }
      return typeMap[intent] || 'info'
    }

    // 获取意图文本
    const getIntentText = (intent) => {
      const textMap = {
        Informational: '信息',
        Commercial: '商业',
        Navigational: '导航',
        Transactional: '交易'
      }
      return textMap[intent] || intent
    }

    // 获取趋势摘要
    const getTrendSummary = (trendData) => {
      try {
        const trends = JSON.parse(trendData)
        if (Array.isArray(trends) && trends.length > 0) {
          const first = trends[0]
          const last = trends[trends.length - 1]
          const change = ((last - first) / first * 100).toFixed(1)
          return change > 0 ? `+${change}%` : `${change}%`
        }
      } catch (error) {
        console.error('解析趋势数据失败:', error)
      }
      return '--'
    }

    // 获取趋势方向
    const getTrendDirection = (trendData) => {
      try {
        const trends = JSON.parse(trendData)
        if (Array.isArray(trends) && trends.length > 0) {
          const first = trends[0]
          const last = trends[trends.length - 1]
          return last > first ? 'trend-up' : 'trend-down'
        }
      } catch (error) {
        console.error('解析趋势数据失败:', error)
      }
      return 'trend-neutral'
    }

    // 获取趋势点数据（用于CSS绘制）
    const getTrendPoints = (trendData) => {
      try {
        const trends = JSON.parse(trendData)
        if (Array.isArray(trends) && trends.length > 0) {
          const min = Math.min(...trends)
          const max = Math.max(...trends)
          const range = max - min || 1

          // 将数据标准化为0-100的百分比
          return trends.map(value => {
            return Math.round(((value - min) / range) * 80 + 10) // 10-90%的范围
          })
        }
      } catch (error) {
        console.error('解析趋势数据失败:', error)
      }
      return []
    }

    // 获取国家名称
    const getCountryName = (countryCode) => {
      const countryMap = {
        global: '全球',
        us: '美国',
        cn: '中国',
        uk: '英国',
        de: '德国',
        fr: '法国',
        jp: '日本',
        kr: '韩国',
        au: '澳大利亚',
        ca: '加拿大'
      }
      return countryMap[countryCode] || countryCode
    }

    // 加载关键词列表
    const loadKeywords = async () => {
      loading.value = true
      try {
        // 映射前端字段名到后端期望的字段名
        const params = {
          keyword: searchForm.keyword,
          intent: searchForm.intent,
          category: searchForm.category,
          tags: searchForm.tags ? [searchForm.tags] : undefined,
          // CPC范围
          min_cpc: searchForm.min_cpc,
          max_cpc: searchForm.max_cpc,
          // 难度范围
          min_difficulty: searchForm.min_difficulty,
          max_difficulty: searchForm.max_difficulty,
          // 竞争密度范围
          min_competitive_density: searchForm.min_competitive_density,
          max_competitive_density: searchForm.max_competitive_density,
          // 高级筛选参数
          min_volume: advancedSearchForm.min_volume,
          max_volume: advancedSearchForm.max_volume,
          trend: advancedSearchForm.trend,
          min_results: advancedSearchForm.min_results,
          max_results: advancedSearchForm.max_results,
          location_ids: advancedSearchForm.location_ids,
          // 分页和排序
          page: pagination.page,
          page_size: pagination.limit,
          sort_by: searchForm.sort_by,
          sort_order: searchForm.sort_order
        }

        // 过滤空值
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === null || params[key] === undefined) {
            delete params[key]
          }
        })

        console.log('搜索参数:', params) // 调试用

        const response = await keywordService.searchKeywords(params)
        console.log('API响应:', response) // 调试用

        // 根据实际返回结构处理数据
        if (response.data) {
          tableData.value = response.data.items || response.data
          pagination.total = response.data.total || 0
          pagination.total_pages = response.data.total_pages || 0
        } else {
          tableData.value = response.items || response
          pagination.total = response.total || 0
          pagination.total_pages = response.total_pages || 0
        }

        console.log('表格数据:', tableData.value) // 调试用
        console.log('分页信息:', pagination) // 调试用
      } catch (error) {
        console.error('加载关键词列表失败:', error)
        if (error.response?.data?.detail) {
          ElMessage.error(`加载失败: ${error.response.data.detail}`)
        } else {
        ElMessage.error('加载关键词列表失败')
        }
      } finally {
        loading.value = false
      }
    }

    // 加载分类列表
    const loadCategories = async () => {
      try {
        const response = await keywordService.getCategories()
        console.log('分类API响应:', response) // 调试用
        
        // 根据实际返回结构处理数据
        if (Array.isArray(response)) {
          categoryList.value = response
        } else if (response.data && Array.isArray(response.data)) {
          categoryList.value = response.data
        } else {
          categoryList.value = []
        }
        
        console.log('分类列表:', categoryList.value) // 调试用
      } catch (error) {
        console.error('加载分类列表失败:', error)
        categoryList.value = []
        // 不显示错误消息，因为这不是关键功能
      }
    }

    // 添加关键词处理
    const handleAdd = async () => {
      if (!formRef.value) return
      
      try {
        const valid = await formRef.value.validate()
        if (!valid) return
        
        loading.value = true
        
        // 处理批量关键词输入 - 用逗号分隔
        const keywordInput = form.keyword.trim()
        if (!keywordInput) {
          ElMessage.warning('请输入关键词')
          loading.value = false
          return
        }
        
        // 分割关键词，去除空白字符
        const keywords = keywordInput
          .split(',')
          .map(kw => kw.trim())
          .filter(kw => kw.length > 0)
        
        if (keywords.length === 0) {
          ElMessage.warning('请输入有效的关键词')
          loading.value = false
          return
        }
        
        // 处理标签数据 - 保持为字符串格式
        const tagsString = form.tags ? form.tags.trim() : ''
        
        let successCount = 0
        const failedKeywords = []
        
        // 批量处理每个关键词
        for (const keyword of keywords) {
          try {
            const keywordData = {
              keyword_name: keyword, // 使用分割后的单个关键词
              avg_monthly_searches: form.searchVolume || 0,
              competition_level: form.competition,
              low_bid_micros: form.cpc ? Math.round(form.cpc * 1000000) : 0,
              high_bid_micros: form.cpc ? Math.round(form.cpc * 1000000) : 0,
              currency_code: "USD",
              language_code: "en",
              location_ids: form.country || "",
              update_method: "manual",
              tags: tagsString,
              category: form.category || null
            }

            // 如果有备注，添加到tags中
            if (form.notes) {
              const additionalTags = [`备注:${form.notes}`]
              
              if (tagsString) {
                keywordData.tags = `${tagsString}, ${additionalTags.join(', ')}`
              } else {
                keywordData.tags = additionalTags.join(', ')
              }
            }

            await keywordService.createKeyword(keywordData)
            successCount++
          } catch (error) {
            console.error(`添加关键词 "${keyword}" 失败:`, error)
            failedKeywords.push(keyword)
          }
        }
        
        // 显示结果消息
        if (successCount > 0) {
          if (failedKeywords.length === 0) {
            ElMessage.success(`成功添加 ${successCount} 个关键词`)
          } else {
            ElMessage.warning(`成功添加 ${successCount} 个关键词，失败 ${failedKeywords.length} 个：${failedKeywords.join(', ')}`)
          }
        } else {
          ElMessage.error('所有关键词添加失败')
        }
        
        // 重置表单
        resetForm()
        
        // 刷新列表
        await loadKeywords()
        // 如果添加了新分类，刷新分类列表
        if (form.category) {
          await loadCategories()
        }
      } catch (error) {
        console.error('添加关键词失败:', error)
        if (error.response?.data?.detail) {
          ElMessage.error(`添加失败: ${JSON.stringify(error.response.data.detail)}`)
        } else {
          ElMessage.error('添加关键词失败')
        }
      } finally {
        loading.value = false
      }
    }

    // 重置表单
    const resetForm = () => {
      if (formRef.value) {
        formRef.value.resetFields()
      }
      Object.assign(form, {
        keyword: '',
        searchVolume: null,
        competition: 'low',
        cpc: null,
        country: 'UK',
        category: '',
        tags: '',
        notes: ''
      })
    }

    // 选择变化处理
    const handleSelectionChange = (selection) => {
      selectedRows.value = selection
      updateSelectAllState()
    }

    // 更新全选状态
    const updateSelectAllState = () => {
      if (isSelectingAll.value) {
        // 如果处于全选状态，保持全选状态
        selectAllChecked.value = true
        selectAllIndeterminate.value = false
      } else {
        // 正常的页面选择状态
        const currentPageTotal = tableData.value.length
        const selectedCount = selectedRows.value.length

        if (selectedCount === 0) {
          selectAllChecked.value = false
          selectAllIndeterminate.value = false
        } else if (selectedCount === currentPageTotal && currentPageTotal > 0) {
          selectAllChecked.value = false // 不自动切换到全选状态
          selectAllIndeterminate.value = true
        } else {
          selectAllChecked.value = false
          selectAllIndeterminate.value = true
        }
      }
    }

    // 获取所有符合筛选条件的关键词ID
    const loadAllFilteredKeywordIds = async () => {
      try {
        const params = {
          keyword: searchForm.keyword,
          intent: searchForm.intent,
          category: searchForm.category,
          tags: searchForm.tags ? [searchForm.tags] : undefined,
          // CPC范围
          min_cpc: searchForm.min_cpc,
          max_cpc: searchForm.max_cpc,
          // 难度范围
          min_difficulty: searchForm.min_difficulty,
          max_difficulty: searchForm.max_difficulty,
          // 竞争密度范围
          min_competitive_density: searchForm.min_competitive_density,
          max_competitive_density: searchForm.max_competitive_density,
          // 高级筛选参数
          min_volume: advancedSearchForm.min_volume,
          max_volume: advancedSearchForm.max_volume,
          trend: advancedSearchForm.trend,
          min_results: advancedSearchForm.min_results,
          max_results: advancedSearchForm.max_results,
          location_ids: advancedSearchForm.location_ids
        }

        // 过滤空值
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === null || params[key] === undefined) {
            delete params[key]
          }
        })

        const response = await keywordService.searchKeywordIds(params)
        allFilteredKeywordIds.value = response.keyword_ids || []
      } catch (error) {
        console.error('获取所有关键词ID失败:', error)
        allFilteredKeywordIds.value = []
      }
    }

    // 处理全选变化
    const handleSelectAllChange = async (checked) => {
      if (checked) {
        // 选中所有
        isSelectingAll.value = true
        selectAllChecked.value = true
        selectAllIndeterminate.value = false

        // 获取所有符合条件的关键词ID
        await loadAllFilteredKeywordIds()

        // 选中当前页面的所有行
        if (tableRef.value) {
          tableRef.value.clearSelection()
          tableData.value.forEach(row => {
            tableRef.value.toggleRowSelection(row, true)
          })
        }
      } else {
        // 取消全选
        isSelectingAll.value = false
        selectAllChecked.value = false
        selectAllIndeterminate.value = false
        allFilteredKeywordIds.value = []

        // 取消选中当前页面的所有行
        if (tableRef.value) {
          tableRef.value.clearSelection()
        }
      }
    }

    // 编辑处理
    const handleEdit = (row) => {
      currentKeyword.value = row
      isEdit.value = true
      showCreateDialog.value = true
    }

    // 添加关键词处理
    const handleAddKeyword = () => {
      currentKeyword.value = null
      isEdit.value = false
      showCreateDialog.value = true
    }

    // 删除处理
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除关键词 "${row.keyword_name}" 吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        loading.value = true
        await keywordService.deleteKeyword(row.id)
        ElMessage.success('删除成功')
        await loadKeywords()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除关键词失败:', error)
          ElMessage.error('删除失败')
        }
      } finally {
        loading.value = false
      }
    }

    // 批量删除处理
    const handleBatchDelete = async () => {
      let keywordIds = []
      let totalCount = 0

      if (isSelectingAll.value) {
        // 全选状态：使用所有符合条件的关键词ID
        keywordIds = allFilteredKeywordIds.value
        totalCount = allFilteredKeywordIds.value.length
      } else {
        // 普通选择状态：使用当前选中的关键词
        if (selectedRows.value.length === 0) {
          ElMessage.warning('请先选择要删除的关键词')
          return
        }
        keywordIds = selectedRows.value.map(row => row.id)
        totalCount = selectedRows.value.length
      }

      if (keywordIds.length === 0) {
        ElMessage.warning('没有可删除的关键词')
        return
      }

      try {
        const baseMessage = isSelectingAll.value
          ? `确定要删除当前筛选条件下的所有 ${totalCount} 个关键词吗？此操作不可恢复。`
          : `确定要删除选中的 ${totalCount} 个关键词吗？此操作不可恢复。`

        const confirmMessage = totalCount > 1000
          ? `${baseMessage}\n\n注意：数据量较大，删除过程将分批进行，请耐心等待。`
          : baseMessage

        await ElMessageBox.confirm(
          confirmMessage,
          '确认批量删除',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: false
          }
        )

        // 如果数据量大于1000，使用分批删除
        if (totalCount > 1000) {
          await handleBatchDeleteWithProgress(keywordIds, totalCount)
        } else {
          // 小批量直接删除
          loading.value = true
          await keywordService.batchDeleteKeywords(keywordIds)
          ElMessage.success(`成功删除 ${totalCount} 个关键词`)
        }

        // 重置选择状态
        isSelectingAll.value = false
        selectAllChecked.value = false
        selectAllIndeterminate.value = false
        allFilteredKeywordIds.value = []
        selectedRows.value = []

        // 重新加载数据
        await loadKeywords()
        await loadCategoryCounts()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量删除关键词失败:', error)
          ElMessage.error('批量删除失败')
        }
      } finally {
        loading.value = false
      }
    }

    // 分批删除处理（带进度显示）
    const handleBatchDeleteWithProgress = async (keywordIds, totalCount) => {
      const batchSize = 2000 // 增大批次大小，减少请求次数
      const totalBatches = Math.ceil(keywordIds.length / batchSize)
      let processedCount = 0
      let successCount = 0
      let failedCount = 0

      // 创建进度提示
      let progressMessage = ElMessage({
        message: `正在删除关键词... 0/${totalCount} (0%)`,
        type: 'info',
        duration: 0,
        showClose: false
      })

      try {
        for (let i = 0; i < totalBatches; i++) {
          const start = i * batchSize
          const end = Math.min(start + batchSize, keywordIds.length)
          const batchIds = keywordIds.slice(start, end)

          console.log(`开始删除第 ${i + 1}/${totalBatches} 批，共 ${batchIds.length} 个关键词`)

          try {
            const result = await keywordService.batchDeleteKeywords(batchIds)
            successCount += result.deleted_count || batchIds.length
            failedCount += result.failed_count || 0

            console.log(`第 ${i + 1} 批删除完成：成功 ${result.deleted_count}，失败 ${result.failed_count}`)
          } catch (error) {
            console.error(`第 ${i + 1} 批删除失败:`, error)
            failedCount += batchIds.length
          }

          processedCount += batchIds.length
          const progress = Math.round((processedCount / totalCount) * 100)

          // 关闭旧的进度提示，创建新的
          progressMessage.close()
          progressMessage = ElMessage({
            message: `正在删除关键词... ${processedCount}/${totalCount} (${progress}%)`,
            type: 'info',
            duration: 0,
            showClose: false
          })

          // 避免请求过于频繁，添加小延迟
          if (i < totalBatches - 1) {
            await new Promise(resolve => setTimeout(resolve, 100))
          }
        }

        // 关闭进度提示
        progressMessage.close()

        // 显示结果
        if (failedCount === 0) {
          ElMessage.success(`成功删除 ${successCount} 个关键词`)
        } else if (successCount > 0) {
          ElMessage.warning(`删除完成：成功 ${successCount} 个，失败 ${failedCount} 个`)
        } else {
          ElMessage.error(`删除失败：${failedCount} 个关键词删除失败`)
        }
      } catch (error) {
        progressMessage.close()
        throw error
      }
    }

    // 导出处理
    const handleExport = async () => {
      try {
        // 映射前端字段名到后端API期望的字段名
        const params = {
          keyword: searchForm.keyword,
          country: searchForm.country, // 映射到后端的country参数
          competition: searchForm.competition, // 映射到后端的competition参数
          category: searchForm.category, // 添加分类过滤参数
          tags: searchForm.tags,
          sort_by: searchForm.sort_by,
          sort_order: searchForm.sort_order,
          format: 'excel'
        }
        
        // 过滤空值
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === null || params[key] === undefined) {
            delete params[key]
          }
        })

        console.log('导出参数:', params) // 调试用
        
        await keywordService.exportKeywords(params)
        ElMessage.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        if (error.response?.data?.detail) {
          ElMessage.error(`导出失败: ${error.response.data.detail}`)
        } else {
          ElMessage.error('导出失败')
        }
      }
    }

    // 对话框确认处理
    const handleDialogConfirm = () => {
      showCreateDialog.value = false
      currentKeyword.value = null
      isEdit.value = false
      loadKeywords()
    }

    // 导入确认处理
    const handleImportConfirm = () => {
      showImportDialog.value = false
      loadKeywords()
    }

    // Google Ads导入处理
    const handleGoogleAdsImport = () => {
      console.log('点击Google Ads按钮，准备打开导入对话框')
      showGoogleAdsImportDialog.value = true
    }

    // Google Ads导入确认处理
    const handleGoogleAdsImportConfirm = () => {
      showGoogleAdsImportDialog.value = false
      loadKeywords()
    }

    // PyTrends导入处理
    const handlePyTrendsImport = () => {
      console.log('点击PyTrends按钮，准备打开导入对话框')
      showPyTrendsImportDialog.value = true
    }

    // PyTrends导入确认处理
    const handlePyTrendsImportConfirm = () => {
      showPyTrendsImportDialog.value = false
      loadKeywords()
    }

    // 搜索处理
    const handleSearch = () => {
      pagination.page = 1
      // 重置全选状态
      isSelectingAll.value = false
      selectAllChecked.value = false
      selectAllIndeterminate.value = false
      allFilteredKeywordIds.value = []
      selectedRows.value = []
      loadKeywords()
    }

    // 自动搜索（防抖处理）
    let searchTimer = null
    const handleAutoSearch = () => {
      // 清除之前的定时器
      if (searchTimer) {
        clearTimeout(searchTimer)
      }

      // 设置新的定时器，500ms后执行搜索
      searchTimer = setTimeout(() => {
        pagination.page = 1
        // 重置全选状态
        isSelectingAll.value = false
        selectAllChecked.value = false
        selectAllIndeterminate.value = false
        allFilteredKeywordIds.value = []
        selectedRows.value = []
        loadKeywords()
      }, 500)
    }

    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        keyword: '',
        intent: '',
        category: '',
        tags: '',
        min_cpc: null,
        max_cpc: null,
        min_difficulty: null,
        max_difficulty: null,
        min_competitive_density: null,
        max_competitive_density: null,
        sort_by: 'updated_at',
        sort_order: 'desc'
      })

      Object.assign(advancedSearchForm, {
        min_volume: null,
        max_volume: null,
        trend: '',
        min_results: null,
        max_results: null,
        location_ids: ''
      })

      // 重置滑块
      difficultyRange.value = [1, 100]
      competitiveDensityRange.value = [0, 1]

      // 重置全选状态
      isSelectingAll.value = false
      selectAllChecked.value = false
      selectAllIndeterminate.value = false
      allFilteredKeywordIds.value = []
      selectedRows.value = []

      pagination.page = 1
      loadKeywords()
    }

    // 重置高级筛选
    const resetAdvancedSearch = () => {
      Object.assign(searchForm, {
        tags: '',
        min_cpc: null,
        max_cpc: null,
        min_competitive_density: null,
        max_competitive_density: null
      })

      Object.assign(advancedSearchForm, {
        min_volume: null,
        max_volume: null,
        trend: '',
        min_results: null,
        max_results: null,
        location_ids: ''
      })

      // 重置滑块
      difficultyRange.value = [1, 100]
      competitiveDensityRange.value = [0, 1]

      pagination.page = 1
      loadKeywords()
    }

    // 难度滑块变化处理
    const handleDifficultyChange = (value) => {
      searchForm.min_difficulty = value[0]
      searchForm.max_difficulty = value[1]
      handleAutoSearch()
    }

    // 竞争密度滑块变化处理
    const handleCompetitiveDensityChange = (value) => {
      searchForm.min_competitive_density = value[0]
      searchForm.max_competitive_density = value[1]
      handleAutoSearch()
    }
        
    // 分类筛选相关方法
    const handleCategorySearch = () => {
      categoryPagination.page = 1
    }

    const handleCategorySortChange = (command) => {
      categorySortOrder.value = command
    }

    const handleCategorySelect = (category) => {
      searchForm.category = category
      pagination.page = 1
      loadKeywords()
    }

    const handleCategoryPageChange = (page) => {
      categoryPagination.page = page
    }

    // 加载分类统计数据
    const loadCategoryCounts = async () => {
      try {
        const response = await keywordService.getCategoriesStats()
        console.log('分类统计API响应:', response) // 调试用
        
        // 根据实际返回结构处理数据
        if (response && typeof response === 'object') {
          categoryCounts.value = response
        } else if (response.data && typeof response.data === 'object') {
          categoryCounts.value = response.data
        } else {
          categoryCounts.value = {}
        }
        
        console.log('分类统计数据:', categoryCounts.value) // 调试用
      } catch (error) {
        console.error('加载分类统计失败:', error)
        categoryCounts.value = {}
      }
    }

    // 表单折叠/展开功能
    const toggleFormCollapse = () => {
      isFormCollapsed.value = !isFormCollapsed.value
    }

    // 排序处理
    const handleSortChange = ({ prop, order }) => {
      console.log('排序变化:', { prop, order })

      // 设置排序字段和方向
      searchForm.sort_by = prop || 'updated_at'
      searchForm.sort_order = order === 'ascending' ? 'asc' : 'desc'

      // 重置到第一页
      pagination.page = 1

      // 重新加载数据
      loadKeywords()
    }

    // 分页处理
    const handleSizeChange = (val) => {
      pagination.limit = val
      pagination.page = 1
      loadKeywords()
    }

    const handleCurrentChange = (val) => {
      pagination.page = val
      loadKeywords()
    }

    // 查看关键词详情
    const viewKeyword = (keyword) => {
      currentKeyword.value = keyword
      showDetailDialog.value = true
    }

    // 从详情页编辑
    const handleEditFromDetail = (keyword) => {
      showDetailDialog.value = false
      currentKeyword.value = keyword
      isEdit.value = true
      showCreateDialog.value = true
    }

    // 刷新数据
    const refreshData = () => {
      loadKeywords()
    }

    // 辅助函数
    const getCompetitionTagType = (level) => {
      const typeMap = {
        low: 'success',
        medium: 'warning',
        high: 'danger',
        unspecified: 'info'
      }
      return typeMap[level] || 'info'
    }

    const getCompetitionText = (level) => {
      const textMap = {
        low: '低',
        medium: '中',
        high: '高',
        unspecified: '未知'
      }
      return textMap[level] || '未知'
    }

    // 格式化数字显示
    const formatNumber = (num) => {
      if (num === null || num === undefined) return '--'

      const number = parseInt(num)
      if (number >= 1000000000) {
        return (number / 1000000000).toFixed(1) + 'B'
      } else if (number >= 1000000) {
        return (number / 1000000).toFixed(1) + 'M'
      } else if (number >= 1000) {
        return (number / 1000).toFixed(1) + 'K'
      } else {
        return number.toLocaleString()
      }
    }

    const getUpdateMethodTagType = (method) => {
      const typeMap = {
        manual: 'primary',
        google_ads_api: 'success',
        batch_import: 'warning'
      }
      return typeMap[method] || 'info'
    }

    const getUpdateMethodText = (method) => {
      const textMap = {
        manual: '手工',
        google_ads_api: 'Google Ads',
        batch_import: '批量导入'
      }
      return textMap[method] || '未知'
    }

    const formatBid = (micros) => {
      if (!micros) return '0'
      return (micros / 1000000).toFixed(2)
    }

    // 组件挂载
    onMounted(() => {
      loadKeywords()
      loadCategories().then(() => {
        loadCategoryCounts()
      })

      // 初始化滑块值
      if (searchForm.min_difficulty && searchForm.max_difficulty) {
        difficultyRange.value = [searchForm.min_difficulty, searchForm.max_difficulty]
      }
      if (searchForm.min_competitive_density !== null && searchForm.max_competitive_density !== null) {
        competitiveDensityRange.value = [searchForm.min_competitive_density, searchForm.max_competitive_density]
      }
    })

    // 监听对话框状态变化
    watch(() => showCreateDialog.value, (newVal) => {
      if (!newVal) {
        currentKeyword.value = null
        isEdit.value = false
      }
    })

    watch(() => showDetailDialog.value, (newVal) => {
      if (!newVal) {
        currentKeyword.value = null
      }
    })

    // 分类管理方法
    const showCategoryContextMenu = (event, category) => {
      contextMenu.visible = true
      contextMenu.x = event.clientX
      contextMenu.y = event.clientY
      contextMenu.category = category
      contextMenu.isUncategorized = false
      searchForm.category = category

      // 点击其他地方隐藏菜单
      const hideMenu = () => {
        contextMenu.visible = false
        document.removeEventListener('click', hideMenu)
      }

      setTimeout(() => {
        document.addEventListener('click', hideMenu)
      }, 100)
    }

    const showUncategorizedContextMenu = (event) => {
      contextMenu.visible = true
      contextMenu.x = event.clientX
      contextMenu.y = event.clientY
      contextMenu.category = 'uncategorized'
      contextMenu.isUncategorized = true

      // 点击其他地方隐藏菜单
      const hideMenu = () => {
        contextMenu.visible = false
        document.removeEventListener('click', hideMenu)
      }

      setTimeout(() => {
        document.addEventListener('click', hideMenu)
      }, 100)
    }

    const showAddCategoryDialog = () => {
      contextMenu.visible = false
      resetCategoryForm()
      addCategoryDialogVisible.value = true
    }

    const showEditCategoryDialog = () => {
      contextMenu.visible = false
      if (!searchForm.category || searchForm.category === '') {
        ElMessage.warning('请先选择要修改的分类')
        return
      }
      
      categoryForm.oldName = searchForm.category
      categoryForm.name = searchForm.category
      editCategoryDialogVisible.value = true
    }

    const confirmDeleteCategory = () => {
      contextMenu.visible = false
      if (!searchForm.category || searchForm.category === '') {
        ElMessage.warning('请先选择要删除的分类')
        return
      }

      // 检查该分类下是否还有关键词
      const categoryKeywordCount = categoryCounts.value[searchForm.category] || 0
      if (categoryKeywordCount > 0) {
        ElMessage.warning(`无法删除分类"${searchForm.category}"，该分类下还有 ${categoryKeywordCount} 个关键词。请先将这些关键词移动到其他分类或删除后再试。`)
        return
      }

      ElMessageBox.confirm(
        `确定要删除分类"${searchForm.category}"吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        handleDeleteCategory()
      }).catch(() => {
        // 用户取消删除
      })
    }

    const confirmClearUncategorized = () => {
      contextMenu.visible = false

      if (!categoryCounts.value.uncategorized || categoryCounts.value.uncategorized === 0) {
        ElMessage.warning('未分类中没有关键词')
        return
      }

      ElMessageBox.confirm(
        `确定要清空未分类中的所有关键词吗？此操作将删除 ${categoryCounts.value.uncategorized} 个关键词，且不可恢复。`,
        '确认清空',
        {
          confirmButtonText: '确定清空',
          cancelButtonText: '取消',
          type: 'warning',
          confirmButtonClass: 'el-button--danger'
        }
      ).then(() => {
        handleClearUncategorized()
      }).catch(() => {
        // 用户取消清空
      })
    }

    const handleAddCategory = async () => {
      if (!addCategoryFormRef.value) return
      
      try {
        await addCategoryFormRef.value.validate()
        categorySubmitting.value = true
        
        // 检查分类是否已存在
        if (categoryList.value.includes(categoryForm.name)) {
          ElMessage.warning('该分类名称已存在')
          return
        }
        
        // 调用API添加分类（这里需要后端提供API）
        await keywordService.createCategory({
          name: categoryForm.name
        })
        
        ElMessage.success('分类添加成功')
        addCategoryDialogVisible.value = false
        resetCategoryForm()
        loadCategories()
        loadKeywords()
      } catch (error) {
        ElMessage.error('添加分类失败: ' + error.message)
      } finally {
        categorySubmitting.value = false
      }
    }

    const handleEditCategory = async () => {
      if (!editCategoryFormRef.value) return
      
      try {
        await editCategoryFormRef.value.validate()
        categorySubmitting.value = true
        
        // 检查新分类名是否已存在
        if (categoryForm.name !== categoryForm.oldName && categoryList.value.includes(categoryForm.name)) {
          ElMessage.warning('该分类名称已存在')
          return
        }
        
        // 调用API修改分类（这里需要后端提供API）
        await keywordService.updateCategory({
          oldName: categoryForm.oldName,
          newName: categoryForm.name
        })
        
        ElMessage.success('分类修改成功')
        editCategoryDialogVisible.value = false
        resetCategoryForm()
        loadCategories()
        loadKeywords()
        
        // 更新当前选中的分类
        searchForm.category = categoryForm.name
      } catch (error) {
        ElMessage.error('修改分类失败: ' + error.message)
      } finally {
        categorySubmitting.value = false
      }
    }

    const handleDeleteCategory = async () => {
      try {
        categorySubmitting.value = true

        // 调用API删除分类（这里需要后端提供API）
        await keywordService.deleteCategory({ name: searchForm.category })

        ElMessage.success('分类删除成功')
        loadCategories()
        loadKeywords()

        // 清除当前选中的分类
        searchForm.category = ''
      } catch (error) {
        ElMessage.error('删除分类失败: ' + error.message)
      } finally {
        categorySubmitting.value = false
      }
    }

    const handleClearUncategorized = async () => {
      try {
        categorySubmitting.value = true

        // 调用API清空未分类关键词
        await keywordService.clearUncategorizedKeywords()

        ElMessage.success('未分类关键词清空成功')
        loadCategories()
        loadKeywords()
        loadCategoryCounts()

        // 如果当前选中的是未分类，切换到全部
        if (searchForm.category === 'uncategorized') {
          searchForm.category = ''
        }
      } catch (error) {
        ElMessage.error('清空未分类失败: ' + error.message)
      } finally {
        categorySubmitting.value = false
      }
    }

    const resetCategoryForm = () => {
      categoryForm.name = ''
      categoryForm.oldName = ''
      if (addCategoryFormRef.value) {
        addCategoryFormRef.value.clearValidate()
      }
      if (editCategoryFormRef.value) {
        editCategoryFormRef.value.clearValidate()
      }
    }

    // 批量更改相关
    const batchUpdateDialogVisible = ref(false)
    const batchUpdateSubmitting = ref(false)
    const batchUpdateFormRef = ref()
    const selectedRows = ref([])

    // 全选相关
    const tableRef = ref()
    const selectAllChecked = ref(false)
    const selectAllIndeterminate = ref(false)
    const allFilteredKeywordIds = ref([])
    const isSelectingAll = ref(false) // 标记是否处于全选状态

    const batchUpdateForm = reactive({
      category: ''
    })
    
    const batchUpdateRules = {
      category: [
        { required: true, message: '请选择新分类', trigger: 'change' }
      ]
    }

    // 批量更改处理
    const handleBatchUpdate = () => {
      if (!isSelectingAll.value && selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要更改的关键词')
        return
      }
      if (isSelectingAll.value && allFilteredKeywordIds.value.length === 0) {
        ElMessage.warning('没有可更改的关键词')
        return
      }
      batchUpdateDialogVisible.value = true
    }

    // 批量更改确认处理
    const handleBatchUpdateConfirm = async () => {
      if (!batchUpdateFormRef.value) return

      try {
        await batchUpdateFormRef.value.validate()
        batchUpdateSubmitting.value = true

        // 获取关键词ID列表
        let keywordIds = []
        let totalCount = 0

        if (isSelectingAll.value) {
          // 全选状态：使用所有符合条件的关键词ID
          keywordIds = allFilteredKeywordIds.value
          totalCount = allFilteredKeywordIds.value.length
        } else {
          // 普通选择状态：使用当前选中的关键词
          keywordIds = selectedRows.value.map(row => row.id)
          totalCount = selectedRows.value.length
        }

        // 调用批量更新API
        await keywordService.batchUpdateCategory(keywordIds, batchUpdateForm.category)

        ElMessage.success(`成功更改 ${totalCount} 个关键词的分类`)
        batchUpdateDialogVisible.value = false
        resetBatchUpdateForm()

        // 重置选择状态
        if (isSelectingAll.value) {
          isSelectingAll.value = false
          selectAllChecked.value = false
          selectAllIndeterminate.value = false
          allFilteredKeywordIds.value = []
        }
        selectedRows.value = []

        // 重新加载数据
        await loadKeywords()
        await loadCategoryCounts()
      } catch (error) {
        console.error('批量更改失败:', error)
        if (error.response?.data?.detail) {
          ElMessage.error(`批量更改失败: ${error.response.data.detail}`)
        } else {
          ElMessage.error('批量更改失败')
        }
      } finally {
        batchUpdateSubmitting.value = false
      }
    }

    // 重置批量更改表单
    const resetBatchUpdateForm = () => {
      batchUpdateForm.category = ''
      if (batchUpdateFormRef.value) {
        batchUpdateFormRef.value.clearValidate()
      }
    }

    return {
      // 响应式数据
      isCollapse,
      loading,
      tableData,
      selectedItems,
      stats,
      categoryList,
      showCreateDialog,
      showImportDialog,
      showGoogleAdsImportDialog,
      showPyTrendsImportDialog,
      showStatsDialog,
      showDetailDialog,
      currentKeyword,
      isEdit,
      searchForm,
      advancedSearchForm,
      isAdvancedSearchExpanded,
      difficultyRange,
      competitiveDensityRange,
      pagination,
      formRef,
      form,
      rules,

      // 分类筛选相关数据
      categorySearchKeyword,
      categoryPageSize,
      categoryPagination,
      categoryCounts,
      filteredCategories,
      paginatedCategories,
      categorySortOrder,

      // 分类管理相关
      addCategoryDialogVisible,
      editCategoryDialogVisible,
      categorySubmitting,
      addCategoryFormRef,
      editCategoryFormRef,
      contextMenu,
      categoryForm,
      categoryRules,

      // 方法
      loadKeywords,
      loadCategories,
      handleAdd,
      resetForm,
      handleSelectionChange,
      handleEdit,
      handleAddKeyword,
      handleDelete,
      handleBatchDelete,
      handleBatchDeleteWithProgress,
      handleExport,
      handleDialogConfirm,
      handleImportConfirm,
      handleGoogleAdsImport,
      handleGoogleAdsImportConfirm,
      handlePyTrendsImport,
      handlePyTrendsImportConfirm,
      handleSearch,
      handleAutoSearch,
      resetSearch,
      resetAdvancedSearch,
      handleDifficultyChange,
      handleCompetitiveDensityChange,
      handleCategorySearch,
      handleCategorySortChange,
      handleCategorySelect,
      handleCategoryPageChange,
      loadCategoryCounts,
      handleSortChange,
      handleSizeChange,
      handleCurrentChange,
      viewKeyword,
      handleEditFromDetail,

      // 分类管理方法
      showCategoryContextMenu,
      showUncategorizedContextMenu,
      showAddCategoryDialog,
      showEditCategoryDialog,
      confirmDeleteCategory,
      confirmClearUncategorized,
      handleAddCategory,
      handleEditCategory,
      handleDeleteCategory,
      handleClearUncategorized,
      resetCategoryForm,

      // 辅助函数
      getCompetitionTagType,
      getCompetitionText,
      getUpdateMethodTagType,
      getUpdateMethodText,
      formatBid,
      formatNumber,
      formatDateTime,
      getIntentTagType,
      getIntentText,
      getTrendSummary,
      getTrendDirection,
      getTrendPoints,
      getCountryName,

      // 表单折叠状态
      isFormCollapsed,
      toggleFormCollapse,

      // 批量更改相关
      batchUpdateDialogVisible,
      batchUpdateSubmitting,
      batchUpdateFormRef,
      selectedRows,
      batchUpdateForm,
      batchUpdateRules,

      // 全选相关
      tableRef,
      selectAllChecked,
      selectAllIndeterminate,
      allFilteredKeywordIds,
      isSelectingAll,

      // 批量更改处理
      handleBatchUpdate,
      handleBatchUpdateConfirm,
      resetBatchUpdateForm,

      // 全选处理
      updateSelectAllState,
      loadAllFilteredKeywordIds,
      handleSelectAllChange
    }
  }
}
</script>

<style scoped>
.keyword-library {
  padding: 0;
  margin: 0;
  width: calc(100vw - 236px); /* 减去侧边栏宽度 + 左边距 */
  height: calc(100vh - 72px); /* 减去header高度 + 上边距 */
  overflow: hidden;
  background: #f5f5f5;
  position: fixed;
  top: 72px; /* header高度 + 上边距8px */
  left: 236px; /* 侧边栏宽度 + 左边距16px */
  z-index: 1;
}

/* 侧边栏收起状态适配 */
.keyword-library.sidebar-collapsed {
  width: calc(100vw - 80px); /* 减去收起侧边栏宽度 + 左边距 */
  left: 80px; /* 收起侧边栏宽度 + 左边距16px */
}

/* 主要布局 */
.main-content {
  display: flex;
  gap: 16px;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden; /* 防止内容溢出 */
}

/* 当表单区域隐藏时，调整布局 */
.main-content:has(.form-section.hidden) {
  gap: 20px;
}

.main-content:has(.form-section.hidden) .category-filter-section {
  flex: 0 0 280px;
  min-width: 280px;
}

.main-content:has(.form-section.hidden) .data-section {
  flex: 1;
  min-width: 0;
}

/* 左侧添加表单区域 */
.form-section {
  flex: 0 0 380px; /* 与手动AI发布页面保持一致的宽度 */
  min-width: 380px;
  transition: all 0.3s ease;
}

.form-section.collapsed {
  flex: 0 0 60px;
  min-width: 60px;
}

.form-section.hidden {
  display: none;
}

.form-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
}

.form-card :deep(.el-card__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #909399 0%, #606266 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.form-card :deep(.el-card__body) {
  padding: 20px;
  height: calc(100vh - 180px);
  min-height: 500px;
  max-height: calc(100vh - 180px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.form-header .header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-header .header-icon {
  font-size: 20px;
  color: white;
}

.collapse-button {
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s ease;
}

.collapse-button:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  transform: scale(1.05);
}

/* 添加表单区域 */
.add-form {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.add-form :deep(.el-form) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.add-form .form-fields {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  margin-right: -8px;
  min-height: 0; /* 重要：允许flex项目收缩 */
}

.add-form .form-buttons {
  flex-shrink: 0;
  flex-grow: 0;
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
  display: flex;
  gap: 8px;
  justify-content: space-between;
  height: 52px; /* 固定按钮区域高度 */
  align-items: center;
}

.add-form .form-button {
  flex: 1;
  height: 36px;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.add-form :deep(.form-button) {
  flex: 1;
  min-width: 0;
}

.add-form :deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.35);
}

.add-form :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.add-form :deep(.el-button--primary:active) {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
}

.add-form :deep(.el-button:not(.el-button--primary)) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #6c757d;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.add-form :deep(.el-button:not(.el-button--primary):hover) {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #495057;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
}

/* 右侧数据管理区域 */
.data-section {
  flex: 1;
  min-width: 0; /* 允许收缩 */
}

.data-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
}

.data-card :deep(.el-card__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.data-card :deep(.el-card__body) {
  padding: 24px;
  height: calc(100% - 76px);
  display: flex;
  flex-direction: column;
}

.data-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.header-left .header-icon {
  font-size: 20px;
  color: white;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 搜索筛选区域 */
.search-section {
  margin-bottom: 24px;
}

.main-filters {
  margin-bottom: 16px;
}

.main-filters .el-col {
  display: flex;
  align-items: center;
  min-height: 60px;
}

.main-filters :deep(.el-input),
.main-filters :deep(.el-select) {
  width: 100%;
}

.main-filters :deep(.el-input__wrapper),
.main-filters :deep(.el-select .el-input__wrapper) {
  height: 36px;
}

.advanced-filters {
  padding: 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.advanced-filters-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.advanced-filters-title .el-icon {
  color: #6c757d;
}

.advanced-row {
  margin-bottom: 20px;
}

.advanced-row:last-child {
  margin-bottom: 0;
}

.filter-item {
  width: 100%;
}

.filter-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}



.advanced-toggle-container {
  display: flex;
  align-items: center;
  height: 100%;
}

.advanced-toggle-btn {
  width: 100%;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.advanced-toggle-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.advanced-toggle-btn .btn-text {
  font-size: 14px;
  white-space: nowrap;
}

.expand-icon {
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.range-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.range-separator {
  color: #909399;
  font-weight: 500;
  flex-shrink: 0;
}

/* 高级筛选中的表单元素样式 */
.advanced-filters :deep(.el-input) {
  width: 100%;
}

.advanced-filters :deep(.el-select) {
  width: 100%;
}

.advanced-filters :deep(.el-input-number) {
  width: 100%;
}

.advanced-filters :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d0d7de;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.advanced-filters :deep(.el-input__wrapper:hover) {
  border-color: #a8b3cf;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.advanced-filters :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.advanced-filters :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

.advanced-filters :deep(.el-input-number .el-input__wrapper) {
  border-radius: 8px;
}

/* 滑块样式 */
.slider-container {
  width: 100%;
  padding: 4px 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 36px;
}

.slider-label {
  display: block;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  line-height: 1.2;
}

.slider-container :deep(.el-slider) {
  margin: 0 8px;
  flex: 1;
}

.slider-container :deep(.el-slider__runway) {
  height: 6px;
  background-color: #e4e7ed;
  border-radius: 3px;
}

.slider-container :deep(.el-slider__bar) {
  height: 6px;
  background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
  border-radius: 3px;
}

.slider-container :deep(.el-slider__button) {
  width: 16px;
  height: 16px;
  border: 2px solid #409eff;
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.slider-container :deep(.el-slider__button:hover) {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.slider-container :deep(.el-slider__marks-text) {
  font-size: 10px;
  color: #909399;
  margin-top: 6px;
  transform: translateX(-50%);
}

.slider-container :deep(.el-slider__stop) {
  width: 4px;
  height: 4px;
  background-color: #c0c4cc;
  border-radius: 50%;
}

/* 高级筛选中的滑块样式 */
.advanced-filters .filter-item :deep(.el-slider) {
  margin: 8px 0;
}

.advanced-filters .filter-item :deep(.el-slider__runway) {
  height: 8px;
  background-color: #f0f2f5;
}

.advanced-filters .filter-item :deep(.el-slider__bar) {
  height: 8px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.advanced-filters .filter-item :deep(.el-slider__button) {
  width: 18px;
  height: 18px;
  border: 3px solid #667eea;
}

.advanced-filters .filter-item :deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.search-section :deep(.el-row) {
  margin-bottom: 16px;
}

.search-section :deep(.el-col) {
  display: flex;
  align-items: center;
}

.search-section :deep(.el-input) {
  width: 100%;
}

.search-section :deep(.el-select) {
  width: 100%;
}

/* 按钮容器样式 */
.button-container {
  display: flex;
  align-items: center;
}

.search-buttons-flex {
  display: flex;
  gap: 6px;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  overflow: visible;
  justify-content: flex-start;
}

.flex-button {
  flex: 0 0 auto !important;
  width: auto !important;
  min-width: fit-content !important;
  padding: 5px 8px !important;
  font-size: 12px !important;
  line-height: 1.3 !important;
  height: auto !important;
  min-height: 26px !important;
  white-space: nowrap !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  box-sizing: border-box !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.flex-button .el-icon {
  margin-right: 3px !important;
  font-size: 12px !important;
}

/* 保持原有的搜索区域按钮样式，但排除flex-button */
.search-section :deep(.el-button:not(.flex-button)) {
  width: 100%;
  font-size: 12px !important;
  padding: 6px 8px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  line-height: 1.2 !important;
  height: 28px !important;
  min-height: 28px !important;
  max-height: 28px !important;
  box-sizing: border-box !important;
}

/* 数据表格 */
.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 表格头部控制区域 */
.table-header-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #fafafa 0%, #f0f2f5 100%);
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.table-header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.table-header-right {
  display: flex;
  align-items: center;
}

.action-buttons-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  height: 32px;
  padding: 0 12px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-btn .el-icon {
  margin-right: 4px;
  font-size: 12px;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.select-all-checkbox {
  font-weight: 600;
  color: #606266;
}

.select-all-checkbox :deep(.el-checkbox__label) {
  font-weight: 600;
  color: #606266;
}

.select-info {
  color: #909399;
  font-size: 13px;
  background: #e8f4fd;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #d4e8fc;
}

.data-card :deep(.el-table) {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
}

.data-card :deep(.el-table__body-wrapper) {
  overflow-y: auto;
}

/* 分页容器 */
.pagination-section {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 表单元素样式优化 */
.search-section :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-section :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-section :deep(.el-input-number .el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-section :deep(.el-button) {
  border-radius: 8px;
  font-weight: 600;
}

.search-section :deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.search-section :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
}

/* 按钮图标样式优化 */
.search-section :deep(.el-button .el-icon) {
  margin-right: 4px !important;
  font-size: 12px !important;
  vertical-align: middle !important;
}

/* 按钮文字样式优化 */
.search-section :deep(.el-button span) {
  font-size: 12px !important;
  line-height: 1.2 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* 针对不同缩放级别的优化 */
@media screen and (min-resolution: 120dpi) {
  .flex-button {
    font-size: 12px !important;
    padding: 5px 10px !important;
    min-height: 26px !important;
  }

  .flex-button .el-icon {
    font-size: 12px !important;
    margin-right: 3px !important;
  }

  .search-section :deep(.el-button:not(.flex-button)) {
    font-size: 11px !important;
    padding: 5px 6px !important;
    height: 26px !important;
    min-height: 26px !important;
    max-height: 26px !important;
  }

  .search-section :deep(.el-button:not(.flex-button) .el-icon) {
    font-size: 11px !important;
    margin-right: 3px !important;
  }

  .search-section :deep(.el-button:not(.flex-button) span) {
    font-size: 11px !important;
  }
}

@media screen and (min-resolution: 144dpi) {
  .flex-button {
    font-size: 11px !important;
    padding: 4px 8px !important;
    min-height: 24px !important;
  }

  .flex-button .el-icon {
    font-size: 11px !important;
    margin-right: 2px !important;
  }

  .search-section :deep(.el-button:not(.flex-button)) {
    font-size: 10px !important;
    padding: 4px 5px !important;
    height: 24px !important;
    min-height: 24px !important;
    max-height: 24px !important;
  }

  .search-section :deep(.el-button:not(.flex-button) .el-icon) {
    font-size: 10px !important;
    margin-right: 2px !important;
  }

  .search-section :deep(.el-button:not(.flex-button) span) {
    font-size: 10px !important;
  }
}

/* 响应式布局优化 */
@media (max-width: 1400px) {
  .search-buttons-flex {
    gap: 4px;
  }

  .flex-button {
    font-size: 11px !important;
    padding: 4px 6px !important;
    min-height: 24px !important;
  }

  .flex-button .el-icon {
    font-size: 11px !important;
    margin-right: 2px !important;
  }
}

@media (max-width: 1200px) {
  .search-buttons-flex {
    gap: 3px;
  }

  .flex-button {
    font-size: 10px !important;
    padding: 3px 5px !important;
    min-height: 22px !important;
  }

  .flex-button .el-icon {
    font-size: 10px !important;
    margin-right: 2px !important;
  }
}

@media (max-width: 768px) {
  .search-buttons-flex {
    flex-direction: column;
    gap: 4px;
    align-items: stretch;
  }

  .flex-button {
    width: 100% !important;
    justify-content: center !important;
    font-size: 12px !important;
    padding: 6px 8px !important;
    min-height: 28px !important;
  }

  .flex-button .el-icon {
    font-size: 12px !important;
    margin-right: 4px !important;
  }
}

/* 高缩放级别优化 (200%+) */
@media screen and (min-resolution: 192dpi) {
  .flex-button {
    font-size: 10px !important;
    padding: 3px 6px !important;
    min-height: 20px !important;
    max-height: 20px !important;
  }

  .flex-button .el-icon {
    font-size: 10px !important;
    margin-right: 2px !important;
  }

  .search-buttons-flex {
    gap: 2px;
  }
}

/* 超高缩放级别优化 (300%+) */
@media screen and (min-resolution: 288dpi) {
  .flex-button {
    font-size: 9px !important;
    padding: 2px 4px !important;
    min-height: 18px !important;
    max-height: 18px !important;
  }

  .flex-button .el-icon {
    font-size: 9px !important;
    margin-right: 1px !important;
  }

  .search-buttons-flex {
    gap: 1px;
    flex-wrap: wrap;
  }
}

/* 浏览器缩放级别检测 */
@media screen and (max-width: 1600px) and (min-width: 1200px) {
  .search-buttons-flex {
    flex-wrap: wrap;
    gap: 4px;
  }
}

@media screen and (max-width: 1000px) {
  .search-buttons-flex {
    flex-wrap: wrap;
    gap: 3px;
  }

  .flex-button {
    font-size: 10px !important;
    padding: 3px 4px !important;
    min-height: 20px !important;
  }
}

/* 通用样式 */
.text-muted {
  color: #999;
}

.bid-range {
  font-size: 12px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-section {
    flex: 0 0 360px;
    min-width: 360px;
  }
  
  .category-filter-section {
    flex: 0 0 200px;
    min-width: 200px;
  }
  
  .add-form :deep(.el-form-item__label) {
    width: 70px;
  }
}

@media (max-width: 768px) {
  .keyword-library {
    width: calc(100vw - 16px);
    left: 8px;
    top: 72px;
    height: calc(100vh - 80px);
  }
  
  .main-content {
    flex-direction: column;
    gap: 12px;
    padding: 12px;
  }
  
  .form-section {
    flex: none;
    min-width: auto;
    height: auto;
  }
  
  .category-filter-section {
    flex: none;
    min-width: auto;
    height: 300px;
  }
  
  .form-card :deep(.el-card__body) {
    height: auto;
    max-height: none;
  }
  
  .add-form :deep(.el-form-item) {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .add-form :deep(.el-form-item__label) {
    width: auto;
    text-align: left;
    padding-right: 0;
    padding-bottom: 6px;
  }
  
  .add-form :deep(.el-form-item__content) {
    width: 100%;
  }
  
  .add-form :deep(.el-form-item.button-group) {
    flex-direction: row;
    align-items: center;
  }
  
  .data-section {
    flex: none;
    height: 60vh;
  }
}

/* 处理浏览器缩放和各种屏幕尺寸 */
@media (min-width: 769px) and (max-width: 1024px) {
  .form-section {
    flex: 0 0 320px;
    min-width: 320px;
  }
}

@media (min-width: 1025px) and (max-width: 1440px) {
  .form-section {
    flex: 0 0 360px;
    min-width: 360px;
  }
}

@media (min-width: 1441px) and (max-width: 1920px) {
  .form-section {
    flex: 0 0 380px;
    min-width: 380px;
  }
}

@media (min-width: 1921px) {
  .form-section {
    flex: 0 0 420px;
    min-width: 420px;
  }
}

/* 操作按钮样式 */
.header-right :deep(.el-button) {
  border-radius: 8px;
  font-weight: 600;
  padding: 12px 24px;
  font-size: 14px;
  min-width: 120px;
  height: 40px;
}

.header-right :deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.header-right :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
}

.header-right :deep(.el-button--success) {
  background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(54, 209, 220, 0.4);
}

.header-right :deep(.el-button--success:hover) {
  background: linear-gradient(135deg, #2fc8d3 0%, #5078d8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(54, 209, 220, 0.5);
}

.header-right :deep(.el-button--info) {
  background: linear-gradient(135deg, #909399 0%, #606266 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 12px rgba(144, 147, 153, 0.4);
}

.header-right :deep(.el-button--info:hover) {
  background: linear-gradient(135deg, #82858a 0%, #565963 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(144, 147, 153, 0.5);
}

.header-right :deep(.el-button--warning) {
  background: linear-gradient(135deg, #E6A23C 0%, #F56C6C 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.4);
}

.header-right :deep(.el-button--warning:hover) {
  background: linear-gradient(135deg, #d18f33 0%, #e85757 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(230, 162, 60, 0.5);
}

/* 表格样式优化 */
.data-card :deep(.el-table__header) {
  background-color: #fafafa;
}

.data-card :deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
  color: #303133;
}

.data-card :deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

.data-card :deep(.el-table__row:hover) {
  background-color: #f8f9ff;
}

.add-form :deep(.el-form-item) {
  margin-bottom: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.add-form :deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  padding-right: 12px;
  width: 80px;
  text-align: right;
  flex-shrink: 0;
}

.add-form :deep(.el-form-item__content) {
  flex: 1;
  margin-left: 0 !important;
}

.add-form :deep(.el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.add-form :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

.add-form :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.25);
}

.add-form :deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.add-form :deep(.el-select .el-input__wrapper:hover) {
  border-color: #c0c4cc;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

.add-form :deep(.el-select .el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.25);
}

.add-form :deep(.el-input-number .el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.add-form :deep(.el-input-number .el-input__wrapper:hover) {
  border-color: #c0c4cc;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

.add-form :deep(.el-input-number .el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.25);
}

.add-form :deep(.el-textarea__inner) {
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  resize: none;
  font-family: inherit;
}

.add-form :deep(.el-textarea__inner:hover) {
  border-color: #c0c4cc;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

.add-form :deep(.el-textarea__inner:focus) {
  border-color: #667eea;
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.25);
}

/* 中间分类筛选栏 */
.category-filter-section {
  flex: 0 0 240px;
  min-width: 240px;
}

.category-filter-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
}

.category-filter-card :deep(.el-card__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.category-filter-card :deep(.el-card__body) {
  padding: 16px;
  height: calc(100% - 76px);
  display: flex;
  flex-direction: column;
}

.category-filter-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.category-filter-header .header-icon {
  font-size: 20px;
  color: white;
}

.category-search {
  margin-bottom: 16px;
}

.category-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  margin-right: -8px;
  display: flex;
  flex-direction: column;
}

.all-category {
  order: -1; /* 确保全部分类显示在最上方 */
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.uncategorized-item {
  order: 999; /* 确保未分类显示在最下方 */
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e5e7eb;
}

.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  margin-bottom: 2px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
  border: none;
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
}

.category-item:hover {
  background: #f3f4f6;
}

.category-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.category-content {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 8px;
  max-width: calc(100% - 60px);
  overflow: hidden;
}

.category-icon {
  font-size: 16px;
  margin-right: 8px;
  color: #6b7280;
  flex-shrink: 0;
}

.category-text {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.category-item:hover .category-icon {
  color: #4b5563;
}

.category-item:hover .category-text {
  color: #4b5563;
}

.category-item.active .category-icon {
  color: white;
}

.category-item.active .category-text {
  color: white;
}

.category-count {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 400;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

.category-item:hover .category-count {
  background: #e5e7eb;
  color: #6b7280;
}

.category-item.active .category-count {
  background: rgba(255, 255, 255, 0.25);
  color: rgba(255, 255, 255, 0.95);
}

.category-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* 折叠状态显示 */
.form-collapsed {
  height: 100%;
  background: linear-gradient(135deg, rgba(144, 147, 153, 0.85) 0%, rgba(96, 98, 102, 0.9) 100%);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.form-collapsed:hover {
  transform: translateX(2px);
  background: linear-gradient(135deg, rgba(144, 147, 153, 0.9) 0%, rgba(96, 98, 102, 0.95) 100%);
  box-shadow: 0 6px 20px rgba(144, 147, 153, 0.25);
  border-color: rgba(255, 255, 255, 0.2);
}

.collapsed-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.collapsed-text {
  font-weight: 600;
  font-size: 22px;
  letter-spacing: 3px;
  line-height: 1.3;
  writing-mode: vertical-lr;
  text-orientation: mixed;
  opacity: 0.95;
}

.collapsed-icon {
  font-size: 42px;
  color: white;
  opacity: 0.9;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* 分类管理按钮样式 */
.category-actions {
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.category-actions .el-button {
  flex: 1;
  min-width: 60px;
  font-size: 12px;
}

/* 删除按钮特殊样式 - 宽度小一点避免误点 */
.category-actions .delete-btn {
  flex: 0 0 auto;
  min-width: 60px;
  max-width: 60px;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  z-index: 9999;
  min-width: 120px;
  padding: 4px 0;
  animation: contextMenuShow 0.2s ease;
}

@keyframes contextMenuShow {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.context-menu-item:hover {
  background-color: #f5f7fa;
}

.context-menu-item.danger {
  color: #f56565;
}

.context-menu-item.danger:hover {
  background-color: #fef5f5;
}

.context-menu-item .el-icon {
  font-size: 16px;
}

.search-with-sort {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-button {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #6c757d;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
}

.sort-button:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #495057;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
}



/* 批量更改对话框样式 */
.batch-update-content {
  padding: 20px 0;
}

.selected-info {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 20px;
  color: #0369a1;
  font-size: 14px;
}

.selected-info strong {
  color: #1e40af;
  font-weight: 600;
}

/* 趋势显示样式 */
.trend-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.trend-chart-simple {
  width: 60px;
  height: 30px;
  display: flex;
  align-items: end;
  gap: 1px;
  padding: 2px;
  border-radius: 2px;
  background: rgba(0, 0, 0, 0.02);
}

.trend-point {
  flex: 1;
  min-height: 2px;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.trend-up .trend-point {
  background: linear-gradient(to top, #67C23A, #85ce61);
}

.trend-down .trend-point {
  background: linear-gradient(to top, #F56C6C, #f78989);
}

.trend-neutral .trend-point {
  background: linear-gradient(to top, #909399, #a6a9ad);
}

.trend-text {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
}

.trend-text {
  font-size: 12px;
  font-weight: 600;
}

.text-muted {
  color: #999;
  font-style: italic;
}

/* 表格中的标签样式 */
:deep(.el-table .el-tag) {
  border-radius: 4px;
  font-size: 11px;
  padding: 2px 6px;
  font-weight: 500;
}

/* 意图标签特殊颜色 */
:deep(.el-tag--info) {
  background-color: #f0f9ff;
  border-color: #bae6fd;
  color: #0369a1;
}

:deep(.el-tag--warning) {
  background-color: #fffbeb;
  border-color: #fed7aa;
  color: #d97706;
}

:deep(.el-tag--primary) {
  background-color: #eff6ff;
  border-color: #bfdbfe;
  color: #2563eb;
}

:deep(.el-tag--success) {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
  color: #16a34a;
}

/* 表头提示样式 */
.table-header-with-help {
  cursor: help;
  transition: color 0.3s ease;
  display: inline-block;
}

.table-header-with-help:hover {
  color: #409eff;
}

/* 宽提示框样式 */
:deep(.wide-tooltip) {
  max-width: 400px !important;
  line-height: 1.5;
}


</style> 