# 站点配置编辑问题修复验证

## 问题描述
编辑定时任务时，站点配置显示为空白，提示"请先选择站点并配置"。

## 根本原因
1. **Schema不匹配**: `ScheduledPublishPlanUpdate` 中 `site_configs` 字段定义为 `Optional[str]`，期望JSON字符串
2. **前端发送格式错误**: 前端发送的是对象数组，但后端期望JSON字符串  
3. **数据处理不一致**: 创建时使用 `json.dumps()` 序列化，但更新时没有相应的处理

## 修复方案

### 1. 后端Schema修改
- 修改 `ScheduledPublishPlanUpdate.site_configs` 字段类型从 `Optional[str]` 改为 `Optional[List[SiteConfigItem]]`
- 将 `SiteConfigItem` 定义移到文件前面，解决导入顺序问题

### 2. 后端API处理逻辑修改
- 在更新API中添加 `site_configs` 字段的序列化处理
- 将前端发送的对象数组转换为JSON字符串存储到数据库

### 3. 前端保持不变
- 前端继续发送对象数组格式的 `site_configs`
- 后端负责处理序列化，保持前后端接口一致性

## 修复的文件

### backend/app/schemas/scheduled_publish.py
```python
# 将 SiteConfigItem 移到前面
class SiteConfigItem(BaseModel):
    site_id: int = Field(..., description="站点ID")
    site_name: str = Field(..., description="站点名称")
    site_url: str = Field(..., description="站点URL")
    keywords: List[str] = Field(..., description="分配的关键词列表")
    blog_category_id: Optional[int] = Field(None, description="文章分类ID")
    blog_category_name: Optional[str] = Field(None, description="文章分类名称")
    blog_tags: Optional[List[int]] = Field(None, description="博客标签ID列表")

# 修改 ScheduledPublishPlanUpdate
class ScheduledPublishPlanUpdate(BaseModel):
    # ... 其他字段 ...
    site_configs: Optional[List[SiteConfigItem]] = Field(None, description="站点配置详情")
    # ... 其他字段 ...
```

### backend/app/api/api_v1/endpoints/scheduled_publish.py
```python
@router.put("/plans/{plan_id}", response_model=ScheduledPublishPlanResponse)
async def update_scheduled_plan(
    plan_id: int,
    plan_data: ScheduledPublishPlanUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新定时发布计划"""
    plan = db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="定时发布计划不存在")
    
    # 处理数据更新，特别处理 site_configs 字段
    update_data = plan_data.model_dump(exclude_unset=True)
    
    # 如果包含 site_configs，需要序列化为JSON字符串
    if 'site_configs' in update_data and update_data['site_configs'] is not None:
        site_configs = update_data['site_configs']
        if isinstance(site_configs, list):
            # 将 SiteConfigItem 对象列表序列化为JSON字符串
            update_data['site_configs'] = json.dumps([
                config.model_dump() if hasattr(config, 'model_dump') else config 
                for config in site_configs
            ])
    
    # 更新字段
    for field, value in update_data.items():
        setattr(plan, field, value)

    db.commit()
    db.refresh(plan)

    # 更新调度器中的计划
    service = ScheduledPublishService(db)
    await service.update_plan_in_scheduler(plan_id)

    return plan
```

## 验证步骤

1. **创建定时任务**: 确保站点配置能正常保存
2. **编辑定时任务**: 验证站点配置能正确加载和显示
3. **更新定时任务**: 确保修改后的站点配置能正确保存

## 预期结果

- 编辑定时任务时，站点配置区域应该显示已保存的配置信息
- 用户可以修改站点配置并成功保存
- 前后端数据格式保持一致，不会出现序列化/反序列化错误

## 测试用例

### 测试数据示例
```json
{
  "site_configs": [
    {
      "site_id": 1,
      "site_name": "测试站点1",
      "site_url": "https://test1.com",
      "keywords": ["关键词1", "关键词2"],
      "blog_category_id": 1,
      "blog_category_name": "技术",
      "blog_tags": [1, 2, 3]
    }
  ]
}
```

### 数据库存储格式
```sql
-- site_configs 字段存储为JSON字符串
UPDATE scheduled_publish_plans 
SET site_configs = '[{"site_id":1,"site_name":"测试站点1",...}]'
WHERE id = 1;
```
