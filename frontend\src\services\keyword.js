import request from '@/utils/request'

// 关键词库服务
export const keywordService = {
  // 搜索关键词
  searchKeywords(params) {
    return request({
      url: '/v1/keyword-library/keywords/search',
      method: 'post',
      data: params
    })
  },

  // 获取符合筛选条件的所有关键词ID
  searchKeywordIds(params) {
    return request({
      url: '/v1/keyword-library/keywords/search-ids',
      method: 'post',
      data: params
    })
  },

  // 获取分类列表
  getCategories() {
    return request({
      url: '/v1/keyword-library/categories',
      method: 'get'
    })
  },

  // 获取分类统计
  getCategoriesStats() {
    return request({
      url: '/v1/keyword-library/categories/stats',
      method: 'get'
    })
  },

  // 获取关键词详情
  getKeyword(id) {
    return request({
      url: `/v1/keyword-library/keywords/${id}`,
      method: 'get'
    })
  },

  // 创建关键词
  createKeyword(data) {
    return request({
      url: '/v1/keyword-library/keywords',
      method: 'post',
      data
    })
  },

  // 更新关键词
  updateKeyword(id, data) {
    return request({
      url: `/v1/keyword-library/keywords/${id}`,
      method: 'put',
      data
    })
  },

  // 删除关键词
  deleteKeyword(id) {
    return request({
      url: `/v1/keyword-library/keywords/${id}`,
      method: 'delete'
    })
  },

  // 批量删除关键词
  batchDeleteKeywords(keywordIds) {
    return request({
      url: '/v1/keyword-library/keywords/batch-delete',
      method: 'post',
      data: { keyword_ids: keywordIds }
    })
  },

  // 批量创建关键词
  batchCreateKeywords(data) {
    return request({
      url: '/v1/keyword-library/keywords/batch',
      method: 'post',
      data
    })
  },

  // 获取统计信息
  getStats() {
    return request({
      url: '/v1/keyword-library/stats',
      method: 'get'
    })
  },

  // 从文件导入
  importFromFile(file, operatorInfo = {}) {
    const formData = new FormData()
    formData.append('file', file)

    if (operatorInfo.operator_id) {
      formData.append('operator_id', operatorInfo.operator_id)
    }
    if (operatorInfo.operator_name) {
      formData.append('operator_name', operatorInfo.operator_name)
    }

    return request({
      url: '/v1/keyword-library/import/file',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 从Semrush文件导入
  importFromSemrush(file, config = {}, operatorInfo = {}) {
    const formData = new FormData()
    formData.append('file', file)

    // 添加Semrush配置参数
    if (config.category) {
      formData.append('category', config.category)
    }
    if (config.country) {
      formData.append('country', config.country)
    }

    if (operatorInfo.operator_id) {
      formData.append('operator_id', operatorInfo.operator_id)
    }
    if (operatorInfo.operator_name) {
      formData.append('operator_name', operatorInfo.operator_name)
    }

    return request({
      url: '/v1/keyword-library/import/semrush',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 查询导入任务状态
  getImportTaskStatus(taskId) {
    return request({
      url: `/v1/keyword-library/import/tasks/${taskId}`,
      method: 'get'
    })
  },

  // 取消导入任务
  cancelImportTask(taskId) {
    return request({
      url: `/v1/keyword-library/import/tasks/${taskId}/cancel`,
      method: 'post'
    })
  },

  // 下载导入模板
  downloadTemplate(templateType = 'csv') {
    const link = document.createElement('a')
    link.href = `/api/v1/keyword-library/import/template/${templateType}`
    link.download = templateType === 'csv' ? '关键词导入模板.csv' : 'keyword_import_template.csv'
    link.click()
  },

  // 从 Google Ads 导入
  importFromGoogleAds(configId, data, operatorInfo = {}) {
    const params = {
      ...data,
      ...operatorInfo
    }
    
    return request({
      url: `/v1/keyword-library/import/google-ads/${configId}`,
      method: 'post',
      data: params
    })
  },

  // 获取关键词历史
  getKeywordHistory(id) {
    return request({
      url: `/v1/keyword-library/keywords/${id}/history`,
      method: 'get'
    })
  },

  // 导出关键词
  exportKeywords(params) {
    return request({
      url: '/v1/keyword-library/export',
      method: 'get',
      params,
      responseType: 'blob'
    }).then(response => {
      // 创建下载链接
      const blob = new Blob([response], {
        type: params.format === 'excel' 
          ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          : 'text/csv'
      })
      
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `keywords.${params.format === 'excel' ? 'xlsx' : 'csv'}`
      link.click()
      window.URL.revokeObjectURL(url)
    })
  },

  // 分类管理相关API
  // 创建分类
  createCategory(data) {
    return request({
      url: '/v1/keyword-library/categories',
      method: 'post',
      data
    })
  },

  // 更新分类
  updateCategory(data) {
    return request({
      url: '/v1/keyword-library/categories',
      method: 'put',
      data
    })
  },

  // 删除分类
  deleteCategory(data) {
    return request({
      url: '/v1/keyword-library/categories',
      method: 'delete',
      data
    })
  },

  // 清空未分类关键词
  clearUncategorizedKeywords() {
    return request({
      url: '/v1/keyword-library/categories/uncategorized',
      method: 'delete'
    })
  },

  // 批量更新关键词分类
  batchUpdateCategory(keywordIds, category) {
    return request({
      url: '/v1/keyword-library/keywords/batch-update-category',
      method: 'post',
      data: {
        keyword_ids: keywordIds,
        category: category
      }
    })
  }
}

// Google Ads 配置服务
export const googleAdsConfigService = {
  // 获取配置列表
  async getConfigs(activeOnly = true) {
    const response = await request.get('/v1/keyword-library/google-ads-configs', {
      params: { active_only: activeOnly }
    })
    return response
  },

  // 获取配置详情
  getConfig(id) {
    return request({
      url: `/v1/keyword-library/google-ads-configs/${id}`,
      method: 'get'
    })
  },

  // 创建配置
  async createConfig(data) {
    const response = await request.post('/v1/keyword-library/google-ads-configs', data)
    return response
  },

  // 更新配置
  async updateConfig(id, data) {
    const response = await request.put(`/v1/keyword-library/google-ads-configs/${id}`, data)
    return response
  },

  // 删除配置
  async deleteConfig(id) {
    const response = await request.delete(`/v1/keyword-library/google-ads-configs/${id}`)
    return response
  },

  // 测试连接
  async testConnection(id) {
    const response = await request.post(`/v1/keyword-library/google-ads-configs/${id}/test`)
    return response
  },

  // OAuth相关方法
  // 启动OAuth授权
  async startOAuthAuthorization(id) {
    const response = await request.post(`/v1/keyword-library/google-ads-configs/${id}/oauth/authorize`)
    return response
  },

  // 获取OAuth状态
  async getOAuthStatus(id) {
    const response = await request.get(`/v1/keyword-library/google-ads-configs/${id}/oauth/status`)
    return response
  },

  // 刷新OAuth令牌
  async refreshOAuthToken(id) {
    const response = await request.post(`/v1/keyword-library/google-ads-configs/${id}/oauth/refresh`)
    return response
  }
}

// PyTrends 配置服务
export const pyTrendsConfigService = {
  // 获取配置列表
  async getConfigs(activeOnly = true) {
    const response = await request.get('/v1/keyword-library/pytrends-configs', {
      params: { active_only: activeOnly }
    })
    return response
  },

  // 获取配置详情
  getConfig(id) {
    return request({
      url: `/v1/keyword-library/pytrends-configs/${id}`,
      method: 'get'
    })
  },

  // 创建配置
  async createConfig(data) {
    const response = await request.post('/v1/keyword-library/pytrends-configs', data)
    return response
  },

  // 更新配置
  async updateConfig(id, data) {
    const response = await request.put(`/v1/keyword-library/pytrends-configs/${id}`, data)
    return response
  },

  // 删除配置
  async deleteConfig(id) {
    const response = await request.delete(`/v1/keyword-library/pytrends-configs/${id}`)
    return response
  },

  // 测试连接
  async testConnection(id) {
    const response = await request.post(`/v1/keyword-library/pytrends-configs/${id}/test`)
    return response
  },

  // 切换配置状态
  async toggleActiveStatus(id) {
    const response = await request.post(`/v1/keyword-library/pytrends-configs/${id}/toggle`)
    return response
  },

  // 获取配置统计
  async getConfigStats(id) {
    const response = await request.get(`/v1/keyword-library/pytrends-configs/${id}/stats`)
    return response
  }
}

// PyTrends 导入服务
export const pytrendsService = {
  // 从 PyTrends 导入关键词
  async importFromPyTrends(configId, data, operatorInfo = {}) {
    const params = {
      ...data,
      ...operatorInfo
    }
    
    const response = await request.post(`/v1/keyword-library/import/pytrends/${configId}`, params, {
      timeout: 180000 // 3分钟超时，给PyTrends处理更多时间
    })
    return response
  },

  // 获取 PyTrends 任务状态
  async getTaskStatus(taskId) {
    const response = await request.get(`/v1/keyword-library/pytrends-tasks/${taskId}`)
    return response
  },

  // 获取 PyTrends 任务历史
  async getTaskHistory(configId) {
    const response = await request.get(`/v1/keyword-library/pytrends-tasks/history/${configId}`)
    return response
  }
}

// 为了保持兼容性，导出keywordLibraryService作为keywordService的别名
export const keywordLibraryService = keywordService 