<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量导入关键词"
    width="600px"
    :before-close="handleClose"
  >
    <div class="import-content">
      <!-- 导入类型选择 -->
      <div class="import-type-section">
        <h4>导入类型</h4>
        <el-radio-group v-model="importType" @change="handleImportTypeChange">
          <el-radio label="standard">标准模板导入</el-radio>
          <el-radio label="semrush">Semrush文件导入</el-radio>
        </el-radio-group>
      </div>

      <!-- 下载模板 -->
      <div v-if="importType === 'standard'" class="template-section">
        <h4>导入模板</h4>
        <div class="template-buttons">
          <el-button @click="downloadTemplate('csv')" icon="el-icon-download" size="small">
            下载 CSV 模板
          </el-button>
          <el-button @click="downloadTemplate('excel')" icon="el-icon-download" size="small">
            下载 Excel 模板
          </el-button>
        </div>
      </div>

      <!-- Semrush导入配置 -->
      <div v-if="importType === 'semrush'" class="semrush-config">
        <el-alert
          title="Semrush导入说明"
          type="success"
          :closable="false"
          show-icon
          size="small"
        >
          <template #default>
            <ul class="info-list">
              <li>支持Semrush导出的CSV和Excel文件</li>
              <li>自动识别Semrush字段结构，无需修改文件</li>
              <li>自动映射：Volume→搜索量、Difficulty→关键词难度、CPC→点击成本等</li>
            </ul>
          </template>
        </el-alert>

        <!-- Semrush导入配置选项 -->
        <div class="semrush-options">
          <div class="option-row">
            <div class="option-item">
              <label class="option-label">关键词分类</label>
              <el-select
                v-model="semrushConfig.category"
                filterable
                allow-create
                default-first-option
                placeholder="选择或输入分类"
                style="width: 200px"
              >
                <el-option
                  v-for="category in categoryOptions"
                  :key="category"
                  :label="category"
                  :value="category"
                />
              </el-select>
            </div>

            <div class="option-item">
              <label class="option-label">目标国家</label>
              <el-select
                v-model="semrushConfig.country"
                filterable
                placeholder="选择国家"
                style="width: 150px"
              >
                <el-option label="全球" value="global" />
                <el-option label="美国" value="us" />
                <el-option label="中国" value="cn" />
                <el-option label="英国" value="uk" />
                <el-option label="德国" value="de" />
                <el-option label="法国" value="fr" />
                <el-option label="日本" value="jp" />
                <el-option label="韩国" value="kr" />
                <el-option label="澳大利亚" value="au" />
                <el-option label="加拿大" value="ca" />
              </el-select>
            </div>
          </div>
        </div>
      </div>

      <!-- 文件上传 -->
      <div class="upload-section">
        <h4>选择文件</h4>
        <el-upload
          ref="uploadRef"
          :file-list="fileList"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :auto-upload="false"
          accept=".csv,.xlsx,.xls"
          :limit="1"
          class="compact-upload"
        >
          <el-button type="primary" size="small">
            <el-icon><upload-filled /></el-icon>
            选择文件
          </el-button>
          <template #tip>
            <div class="el-upload__tip">
              支持 .csv/.xlsx/.xls 文件，大小不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 字段说明（折叠） -->
      <div class="field-info">
        <el-collapse v-model="activeFieldInfo" accordion>
          <el-collapse-item title="字段说明" name="field-help">
            <div class="field-sections">
              <div class="field-section">
                <h5>新字段</h5>
                <div class="field-list">
                  <div class="field-item">
                    <strong>意图</strong>：关键词意图类型（Informational/Commercial/Navigational/Transactional）
                  </div>
                  <div class="field-item">
                    <strong>搜索量</strong>：关键词的月搜索量数值
                  </div>
                  <div class="field-item">
                    <strong>趋势</strong>：12个月趋势数据，JSON数组格式
                  </div>
                  <div class="field-item">
                    <strong>关键词难度</strong>：SEO难度评分（0-100）
                  </div>
                  <div class="field-item">
                    <strong>CPC (USD)</strong>：每次点击费用，美元单位
                  </div>
                  <div class="field-item">
                    <strong>竞争密度</strong>：广告竞争密度（0-1）
                  </div>
                  <div class="field-item">
                    <strong>SERP特征</strong>：搜索结果页特征，JSON数组格式
                  </div>
                  <div class="field-item">
                    <strong>搜索结果数</strong>：搜索结果总数量
                  </div>
                </div>
              </div>
              <div class="field-section">
                <h5>兼容字段</h5>
                <div class="field-list">
                  <div class="field-item">
                    <strong>平均每月搜索量</strong>：兼容字段，建议使用"搜索量"字段
                  </div>
                  <div class="field-item">
                    <strong>竞争级别</strong>：低/中/高/未知
                  </div>
                  <div class="field-item">
                    <strong>竞争指数</strong>：竞争激烈程度（0-100）
                  </div>
                  <div class="field-item">
                    <strong>出价第20/80百分位</strong>：出价建议范围（微货币单位）
                  </div>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>



      <!-- 导入进度 -->
      <div v-if="importProgress.show" class="progress-section">
        <h4>导入进度</h4>
        <div class="progress-info">
          <el-progress
            :percentage="importProgress.percentage"
            :status="importProgress.status"
            :stroke-width="10"
          />
          <p class="progress-text">{{ importProgress.text }}</p>
          <div v-if="importProgress.result" class="progress-result">
            <el-tag type="success">新增: {{ importProgress.result.success_count }}</el-tag>
            <el-tag v-if="importProgress.result.updated_count > 0" type="warning" style="margin-left: 10px">
              更新: {{ importProgress.result.updated_count }}
            </el-tag>
            <el-tag type="danger" style="margin-left: 10px">失败: {{ importProgress.result.failed_count }}</el-tag>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="!isImportInProgress"
          type="primary"
          @click="handleImport"
          :loading="importing"
          :disabled="!selectedFile"
        >
          开始导入
        </el-button>
        <el-button
          v-else
          type="danger"
          @click="handleStopImport"
        >
          停止导入
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { keywordService } from '@/services/keyword'

export default {
  name: 'ImportDialog',
  components: {
    UploadFilled
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible', 'success', 'refresh-categories'],
  setup(props, { emit }) {
    const uploadRef = ref()
    const importing = ref(false)
    const isImportInProgress = ref(false) // 导入是否正在进行中
    const selectedFile = ref(null)
    const fileList = ref([])
    const pollTimer = ref(null)
    const currentTaskId = ref(null) // 当前导入任务ID
    const activeFieldInfo = ref('')
    const importType = ref('standard') // 'standard' 或 'semrush'

    // Semrush导入配置
    const semrushConfig = ref({
      category: '',
      country: 'global'
    })

    // 分类选项
    const categoryOptions = ref([])

    // 加载分类列表
    const loadCategories = async () => {
      try {
        const response = await keywordService.getCategories()
        if (response && response.length > 0) {
          // 只使用从后端加载的分类
          const existingCategories = response.map(cat => cat.name || cat)
          categoryOptions.value = existingCategories
        }
      } catch (error) {
        console.error('加载分类列表失败:', error)
      }
    }

    // 计算属性
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })

    // 监听对话框打开，加载分类
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        loadCategories()
      }
    })

    // 导入进度
    const importProgress = reactive({
      show: false,
      percentage: 0,
      status: '',
      text: '',
      result: null
    })

    // 文件上传前检查
    const beforeUpload = (file) => {
      // 检查文件类型
      const isValidType = ['csv', 'xlsx', 'xls'].some(ext => 
        file.name.toLowerCase().endsWith(`.${ext}`)
      )
      if (!isValidType) {
        ElMessage.error('请上传 CSV 或 Excel 文件！')
        return false
      }

      // 检查文件大小 (10MB)
      const isValidSize = file.size / 1024 / 1024 < 10
      if (!isValidSize) {
        ElMessage.error('文件大小不能超过 10MB！')
        return false
      }

      return false // 阻止自动上传
    }

    // 文件变化处理
    const handleFileChange = (file) => {
      selectedFile.value = file.raw
      fileList.value = [file]
    }

    // 文件移除处理
    const handleFileRemove = () => {
      selectedFile.value = null
      fileList.value = []
    }

    // 下载模板
    const downloadTemplate = (format) => {
      try {
        if (format === 'csv') {
          // 使用服务方法下载模板文件
          keywordService.downloadTemplate('csv')
        } else {
          // Excel格式暂时使用CSV格式
          keywordService.downloadTemplate('csv')
          ElMessage.info('已下载CSV格式模板，Excel格式功能正在开发中')
        }
      } catch (error) {
        console.error('下载模板失败:', error)
        ElMessage.error('下载模板失败，请稍后重试')
      }
    }

    // 轮询导入状态
    const pollImportStatus = (taskId) => {
      pollTimer.value = setInterval(async () => {
        try {
          const task = await keywordService.getImportTaskStatus(taskId)

          if (task.status === 'processing') {
            isImportInProgress.value = true

            // 从error_message中解析更新数量
            let updatedCount = 0
            if (task.error_message && task.error_message.includes('更新:')) {
              const match = task.error_message.match(/更新:\s*(\d+)/)
              if (match) {
                updatedCount = parseInt(match[1]) || 0
              }
            }

            // 计算总处理数量：新增 + 更新 + 失败
            const processedCount = (task.success_count || 0) + (task.failed_count || 0) + updatedCount
            const totalCount = task.total_count || 0

            // 计算实际进度百分比
            let actualProgress = 0
            if (totalCount > 0) {
              actualProgress = Math.round((processedCount / totalCount) * 100)
            }

            importProgress.percentage = actualProgress
            importProgress.text = `正在处理中... ${processedCount}/${totalCount} (${actualProgress}%)`
          } else if (task.status === 'completed') {
            importProgress.percentage = 100
            importProgress.status = 'success'
            // 解析更新统计信息
            let updatedCount = 0
            if (task.error_message && task.error_message.includes('更新:')) {
              const match = task.error_message.match(/更新:\s*(\d+)/)
              if (match) {
                updatedCount = parseInt(match[1])
              }
            }

            // 显示详细的完成信息
            const totalProcessed = (task.success_count || 0) + (task.failed_count || 0) + updatedCount
            importProgress.text = `导入完成！${totalProcessed}/${task.total_count || 0} (成功: ${task.success_count || 0}, 更新: ${updatedCount}, 失败: ${task.failed_count || 0})`

            importProgress.result = {
              success_count: task.success_count,
              failed_count: task.failed_count,
              updated_count: updatedCount
            }

            clearInterval(pollTimer.value)
            isImportInProgress.value = false

            // 构建消息
            let message = `导入完成！新增 ${task.success_count} 条`
            if (updatedCount > 0) {
              message += `，更新 ${updatedCount} 条`
            }
            if (task.failed_count > 0) {
              message += `，失败 ${task.failed_count} 条`
            }

            ElMessage.success(message)
            
            // 延迟关闭进度显示
            setTimeout(() => {
              emit('success')
              // 触发分类导航刷新
              emit('refresh-categories')
            }, 2000)
          } else if (task.status === 'failed') {
            importProgress.percentage = 100
            importProgress.status = 'exception'
            importProgress.text = '导入失败：' + (task.error_message || '未知错误')

            clearInterval(pollTimer.value)
            isImportInProgress.value = false
            ElMessage.error('导入失败：' + (task.error_message || '未知错误'))
          } else if (task.status === 'cancelled') {
            importProgress.percentage = 100
            importProgress.status = 'warning'
            importProgress.text = '导入已取消'

            clearInterval(pollTimer.value)
            isImportInProgress.value = false
            ElMessage.warning('导入已取消')
          }
        } catch (error) {
          console.error('查询导入状态失败:', error)
          clearInterval(pollTimer.value)
          importProgress.status = 'exception'
          importProgress.text = '查询导入状态失败'
        }
      }, 1000) // 改为每1秒轮询一次，提高响应速度
    }

    // 停止导入
    const handleStopImport = async () => {
      try {
        await ElMessageBox.confirm('确定要停止导入吗？正在处理的数据将会保留。', '确认停止', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用后端API取消任务
        if (currentTaskId.value) {
          try {
            await keywordService.cancelImportTask(currentTaskId.value)
            ElMessage.success('导入任务已取消')
          } catch (error) {
            console.error('取消导入任务失败:', error)
            ElMessage.error('取消导入任务失败')
          }
        }

        // 停止轮询
        if (pollTimer.value) {
          clearInterval(pollTimer.value)
        }

        // 重置状态
        importing.value = false
        isImportInProgress.value = false
        importProgress.show = false
        importProgress.percentage = 0
        importProgress.status = ''
        importProgress.text = ''
      } catch {
        // 用户取消停止操作
      }
    }

    // 开始导入
    const handleImport = async () => {
      if (!selectedFile.value) {
        ElMessage.warning('请先选择要导入的文件')
        return
      }

      try {
        importing.value = true
        isImportInProgress.value = true

        // 显示进度
        importProgress.show = true
        importProgress.percentage = 0
        importProgress.status = ''
        importProgress.text = '正在上传文件...'
        importProgress.result = null

        // 根据导入类型选择不同的API
        let response
        if (importType.value === 'semrush') {
          // 验证Semrush配置
          if (!semrushConfig.value.category) {
            ElMessage.warning('请选择或输入关键词分类')
            return
          }

          response = await keywordService.importFromSemrush(
            selectedFile.value,
            {
              category: semrushConfig.value.category,
              country: semrushConfig.value.country
            }
          )
        } else {
          response = await keywordService.importFromFile(selectedFile.value)
        }
        
        importProgress.percentage = 0
        importProgress.text = '文件上传成功，开始处理...'

        // 保存任务ID并开始轮询状态
        currentTaskId.value = response.task_id
        pollImportStatus(response.task_id)
      } catch (error) {
        console.error('导入失败:', error)
        importing.value = false
        isImportInProgress.value = false
        importProgress.show = false
        
        if (error.response?.data?.detail) {
          ElMessage.error(error.response.data.detail)
        } else {
          ElMessage.error('导入失败')
        }
      } finally {
        importing.value = false
      }
    }

    // 导入类型变化处理
    const handleImportTypeChange = (value) => {
      // 清空已选择的文件
      selectedFile.value = null
      fileList.value = []

      // 重置Semrush配置
      if (value === 'semrush') {
        semrushConfig.value = {
          category: '',
          country: 'global'
        }
      }
    }

    // 关闭对话框
    const handleClose = () => {
      if (pollTimer.value) {
        clearInterval(pollTimer.value)
      }

      // 重置所有状态
      selectedFile.value = null
      fileList.value = []
      importing.value = false
      isImportInProgress.value = false
      currentTaskId.value = null
      importProgress.show = false
      importProgress.percentage = 0
      importProgress.status = ''
      importProgress.text = ''
      importProgress.result = null

      // 重置导入类型和配置
      importType.value = 'standard'
      semrushConfig.value = {
        category: '',
        country: 'global'
      }

      // 重置上传组件
      if (uploadRef.value) {
        uploadRef.value.clearFiles()
      }

      dialogVisible.value = false
    }

    return {
      uploadRef,
      importing,
      isImportInProgress,
      currentTaskId,
      selectedFile,
      fileList,
      dialogVisible,
      importProgress,
      activeFieldInfo,
      importType,
      semrushConfig,
      categoryOptions,
      loadCategories,
      beforeUpload,
      handleFileChange,
      handleFileRemove,
      downloadTemplate,
      handleImport,
      handleStopImport,
      handleImportTypeChange,
      handleClose
    }
  }
}
</script>

<style scoped>
.import-content {
  max-height: 70vh;
  overflow-y: auto;
}

.info-list {
  margin: 0;
  padding-left: 20px;
}

.info-list li {
  margin: 5px 0;
  color: #606266;
  font-size: 13px;
}

.field-info {
  margin: 15px 0 0 0;
}

.field-sections {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.field-section h5 {
  margin: 0 0 10px 0;
  color: #409eff;
  font-size: 14px;
  font-weight: 600;
}

.field-list {
  padding: 0;
}

.field-item {
  margin: 6px 0;
  padding: 6px 10px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.4;
}

.field-item strong {
  color: #409eff;
  margin-right: 6px;
}

.import-type-section,
.template-section,
.semrush-info,
.upload-section,
.progress-section {
  margin: 15px 0;
}

.import-type-section h4,
.template-section h4,
.upload-section h4,
.progress-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 15px;
  font-weight: 600;
}

.template-buttons {
  display: flex;
  gap: 10px;
}

.compact-upload {
  margin-top: 5px;
}

.compact-upload .el-upload__tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.semrush-config {
  margin: 15px 0;
}

.semrush-options {
  margin-top: 15px;
}

.option-row {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.option-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.option-label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.template-section h4,
.upload-section h4,
.progress-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.template-buttons {
  display: flex;
  gap: 10px;
}

.progress-text {
  margin: 10px 0;
  color: #666;
  font-size: 14px;
}

.progress-result {
  margin-top: 15px;
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
}

.progress-section {
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

.progress-info {
  text-align: center;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
}

:deep(.el-upload-dragger .el-icon--upload) {
  font-size: 28px;
  color: #c0c4cc;
  margin: 20px 0 16px;
  line-height: 50px;
}

:deep(.el-upload__text) {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

:deep(.el-upload__text em) {
  color: #409eff;
  font-style: normal;
}

:deep(.el-upload__tip) {
  font-size: 12px;
  color: #606266;
  margin-top: 7px;
}
</style> 