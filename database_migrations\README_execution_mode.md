# 数据库迁移：添加执行模式字段

## 概述
为定时发布计划表 `scheduled_publish_plans` 添加 `execution_mode` 字段，支持"仅执行一次"任务的执行时间选择功能。

## 变更内容

### 新增字段
- **字段名**: `execution_mode`
- **类型**: `VARCHAR(20)`
- **允许空值**: `YES`
- **默认值**: `NULL`
- **注释**: `执行模式：immediate(立即执行) 或 scheduled(定时执行)`

### 字段用途
- `immediate`: 立即执行 - 任务创建后直接加入执行队列
- `scheduled`: 定时执行 - 任务创建后等待调度器在指定时间触发
- `NULL`: 不适用 - 用于非"仅执行一次"的任务（每天、每周、自定义间隔）

## 执行步骤

### 1. 执行迁移
```bash
mysql -u your_username -p your_database < add_execution_mode_field.sql
```

### 2. 验证迁移
执行迁移后，检查以下内容：
- 字段是否成功添加
- 现有数据的 `execution_mode` 是否正确设置
- 应用程序是否正常运行

### 3. 回滚（如需要）
```bash
mysql -u your_username -p your_database < rollback_execution_mode_field.sql
```

## 数据迁移策略

### 现有数据处理
- 所有 `frequency_type = 'once'` 的现有计划将被设置为 `execution_mode = 'scheduled'`
- 其他频率类型的计划保持 `execution_mode = NULL`

### 新数据处理
- 前端提交时会根据用户选择设置相应的 `execution_mode` 值
- 后端根据 `execution_mode` 决定任务的执行方式

## 影响范围

### 前端变更
- 配置对话框新增执行时间选择界面
- 表单数据结构扩展
- 提交逻辑更新

### 后端变更
- 数据库模型更新
- API Schema 扩展
- 任务执行逻辑分支

### 兼容性
- **向后兼容**: 现有功能不受影响
- **数据兼容**: 现有数据自动迁移
- **API兼容**: 新字段为可选，不影响现有调用

## 测试建议

### 功能测试
1. 创建"立即执行"任务，验证是否立即加入队列
2. 创建"定时执行"任务，验证是否正确调度
3. 编辑现有计划，验证执行模式选择
4. 测试其他频率类型是否正常工作

### 数据测试
1. 验证现有计划的 `execution_mode` 值
2. 测试新创建计划的字段设置
3. 检查数据库约束和索引

## 注意事项

1. **备份数据**: 执行迁移前建议备份数据库
2. **停机时间**: 迁移过程可能需要短暂停机
3. **监控**: 迁移后密切监控应用程序运行状态
4. **回滚准备**: 准备好回滚脚本以防出现问题

## 联系信息
如有问题，请联系开发团队。
